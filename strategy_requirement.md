## 一、任务概述

现在我需要用python实现一个投资策略生成框架，使用polars进行数据处理，以面向对象的形式进行设计。我希望将不同的行情信号下可用的策略抽象成原语，并使用一个生成式引擎将来自不同信号的策略进行排列组合，完整遍历每一种策略的组合，并运行多空回测，最后将回测结果、策略条件、指标参数全部记录下来

## 二、功能需求

### 1. 前置知识
请参考如下代码，这是原有框架的signal类实现：

```python
import sys
import polars as pl
from datetime import datetime

from quant import BaseSignal
from core.log import LK


"""
df:
┌─────────────────────┬─────────────────────┬────────────────┬────────────────┬───┬────────────────┬─────────────┬─────────────────┬────────────┐
│ start_time          ┆ end_time            ┆ open           ┆ high           ┆ … ┆ close          ┆ vol         ┆ money           ┆ trades_cnt │
│ ---                 ┆ ---                 ┆ ---            ┆ ---            ┆   ┆ ---            ┆ ---         ┆ ---             ┆ ---        │
│ i64                 ┆ i64                 ┆ str            ┆ str            ┆   ┆ str            ┆ str         ┆ str             ┆ i64        │
╞═════════════════════╪═════════════════════╪════════════════╪════════════════╪═══╪════════════════╪═════════════╪═════════════════╪════════════╡
"""

class BollingerSignal(BaseSignal):
    # from indicator import init_logger  # 延迟导入
    # init_logger()
    
    def calculate_bollinger_bands(self, df: pl.DataFrame, window: int = 20, num_std: float = 2) -> pl.DataFrame:
        """
        计算布林带指标（Bollinger Bands）
        
        参数:
            df: 包含股票数据的Polars DataFrame，需包含'datetime'和'close'列
            window: 移动平均窗口大小（默认20）
            num_std: 标准差倍数（默认2）
        
        返回:
            添加了布林带三轨的DataFrame
        """
        # 确保数据按时间排序
        df = df.sort("datetime")
        
        # 将价格列转换为浮点数类型（如果原始数据是字符串）
        if df["close"].dtype == pl.Utf8:
            df = df.with_columns(pl.col("close").cast(pl.Float64))
        
        # 计算中轨（20日移动平均）
        middle_band = pl.col("close").rolling_mean(window_size=window)
        
        # 计算标准差
        std_dev = pl.col("close").rolling_std(window_size=window)
        
        # 计算上轨和下轨
        upper_band = middle_band + (std_dev * num_std)
        lower_band = middle_band - (std_dev * num_std)
        bandwidth = upper_band - lower_band
        
        # 添加三轨到原始DataFrame
        result = df.with_columns(
            middle_band.alias("middle_band"),
            upper_band.alias("upper_band"),
            lower_band.alias("lower_band"),
            bandwidth.alias("bandwidth")
        )
        
        return result
    
    def bollinger_crossing_mid(self, df:pl.DataFrame, window:int = 10, num_std:float = 2, lookback = 5):
        df = self.calculate_bollinger_bands(df, window, num_std)
        tolerance = 30
        prev_conditions_long0 = [
            (pl.col("close").shift(i) > pl.col("lower_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        condition1 = ((pl.col("low") - pl.col("lower_band")).abs() < tolerance) & pl.all_horizontal(prev_conditions_long0) & ((pl.col("low") - pl.col("lower_band")) < 0)

        prev_conditions_long = [
            (pl.col("close").shift(i) < pl.col("middle_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        condition2 = ((pl.col("close") - pl.col("middle_band")).abs() < tolerance) & pl.all_horizontal(prev_conditions_long) & ((pl.col("close") - pl.col("middle_band")) > 0)
        condition2 = None
        df_long = df.filter(condition1|condition2)

        prev_conditions_short = [
            (pl.col("close").shift(i) > pl.col("middle_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        condition3 = ((pl.col("close") - pl.col("middle_band")).abs() < tolerance) & pl.all_horizontal(prev_conditions_short)
        df_short = df.filter(condition3)

        LK.info(f"window:{window}, num_std:{num_std}, lookback:{lookback}")

        return [
            {
                "name" : "Long",
                "start_time" : df_long["start_time"].to_list(),
                "datetime" : df_long["datetime"].to_list(),
                "marker" : dict(color='yellow', size=10)
            },
            {
                "name" : "Short",
                "start_time" : df_short["start_time"].to_list(),
                "datetime" : df_short["datetime"].to_list(),
                "marker" : dict(color='blue', size=10)
            }
        ]
    
    def bollinger_crossing_band(self, df:pl.DataFrame, window:int = 10, num_std:float = 2, lookback = 3):
        df = self.calculate_bollinger_bands(df, window, num_std)

        df_long = []
        df_short = []
        prev_conditions_long = [
            (pl.col("high").shift(i) > pl.col("upper_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        condition1 = pl.all_horizontal(prev_conditions_long)
        df_long = df.filter(condition1)

        LK.info(f"long conditions: {condition1}")
        LK.info(f"window:{window}, num_std:{num_std}, lookback:{lookback}, {len(df_long)}/{df.height} long points, {len(df_short)}/{df.height} short points")
        return [
            {
                "name" : "Long",
                "start_time" : df_long["start_time"].to_list(),
                "datetime" : df_long["datetime"].to_list(),
                "marker" : dict(color='yellow', size=10)
            },
        ]
    
    def bollinger_bandwidth(self, df:pl.DataFrame, window:int = 10, num_std:float = 2, lookback = 3):
        df = self.calculate_bollinger_bands(df, window, num_std)
        df_long = []
        df_short = []

        prev_conditions = [
            (pl.col("bandwidth").shift(i) < pl.col("bandwidth").shift(i-1))
            for i in range(1, lookback + 1)
        ]
        prev_long = [
            (pl.col("high").shift(i) > pl.col("upper_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        long_condition = pl.all_horizontal(prev_long) & pl.all_horizontal(prev_conditions)
        df_long = df.filter(prev_conditions)

        short_condition = (pl.col("low") < pl.col("lower_band")) & pl.all_horizontal(prev_conditions)
        df_short = df.filter(prev_conditions)
        
        LK.info(f"long conditions: {long_condition}")
        LK.info(f"short conditions: {short_condition}")
        LK.info(f"window:{window}, num_std:{num_std}, lookback:{lookback}, {len(df_long)}/{df.height} long points, {len(df_short)}/{df.height} short points")
        return [
            {
                "name" : "Long",
                "start_time" : df_long["start_time"].to_list(),
                "datetime" : df_long["datetime"].to_list(),
                "marker" : dict(color='yellow', size=10)
            },
            {
                "name" : "Short",
                "start_time" : df_short["start_time"].to_list(),
                "datetime" : df_short["datetime"].to_list(),
                "marker" : dict(color='blue', size=10)
            }
        ]
        
    def gen(self, df:pl.DataFrame):
        return self.bollinger_bandwidth(df)
```

### 2. 信号-策略
如二.1中代码所示，BollingerSignal是一个信号（即布林带指标），而bollinger_bandwidth、bollinger_crossing_band、bollinger_crossing_mid是基于布林带的几种进场策略。现在我希望你设计一个架构，使得信号、策略都可以抽象出来，而策略是一个最小的执行单元，用于之后的跨信号策略组合生成。至于信号，它可以单纯是一个目录名用于归类各个策略，也可以是一个类。在这里，我给出我的粗略设计：

BaseStrategyClass->StrategyRealizationClass \
Signal1/Strategy1.py, Strategy2.py...

或: \
BaseStrategyClass->StrategyRealizationClass \
BaseSignalClass->SignalRealizationClass 

class Signal1(BaseSignal): \
    self.strategy = [Strategy1, Strategy2...]
    

如果你认为这个设计有待完善，需要使用面向对象的设计/泛型或模板编程，包含一定的成员变量/控制访问权限等，也可以提出自己的想法。

### 3. 参数类
如以下代码所示：
```python
def calculate_bollinger_bands(self, df: pl.DataFrame, window: int = 20, num_std: float = 2) -> pl.DataFrame:

def bollinger_bandwidth(self, df:pl.DataFrame, window:int = 10, num_std:float = 2, lookback = 3):
```
每个信号有自己的参数组合，而该信号的策略也有自己的参数组合。为了方便生成式引擎对策略进行生成，也许最好应该把信号和策略的参数都抽象成一个类，以配置+注入的方式进行自动化的调用?

目前我对此并没有什么想法，请你参考我的需求：解耦参数与信号、策略的具体实现，以便自动化调用，帮助我设计这一层面的代码框架。

### 4. 策略组合生成引擎
有了原语级的策略实现类之后，接下来就是遍历所有可能的策略组合，得到最终的做多/做空入场条件了。以以下代码为例：

```python
class Signal1Strategy1(BaseStrategy):
    self.param = dict()
    def get_condition(self, df:pl.Dataframe):
        # 调用参数，进行数值计算等业务逻辑
        prev_long_conditions = pl.col("xx")....pl.col("yy")
        prev_long_conditions = pl.col("xx")....pl.col("yy")
        long_condition = pl.all_horizontal(prev_long_conditions)
        short_condition = pl.all_horizontal(prev_short_conditions) # 或者是其他的逻辑运算组合,但是一定是polars库支持的，并且可以保存在变量内print出来的
        return long_condition, short_condition
    def _get_params(self):
        pass

class Signal1(BaseSignal):
    self.strategies = [Signal1Strategy1, Signal1Strategy2,...]
    def get_strategy(self, i):
        return self.strategies[i]

class StrategyEngine(BaseClass):
    self.signals = [Signal1, Signal2, ...]

    def generate_strategies(self):
        strategies = self._dfs(self.signals) 
        """
        对于每个signal，只允许贡献一个strategy。合法组合形式为:
        [
            [Signal1.Signal1Strategy1, Signal2.Signal2Strategy1, ...],
            [Signal1.Signal1Strategy2, Signal2.Signal2Strategy1, ...],...
            [Signal1.Signal1StrategyN, Signal2.Signal2StrategyN, ...]
        ]
        不允许
        [Signal1.Signal1Strategy1, Signal1.Signal1Strategy2, ...],的结果
        """
        
        return strategies

```

我希望实现一个策略生成引擎，可以对每个signal的策略选取一个，并与其它signal的策略进行组合，生成一个strategies矩阵，这个矩阵中每一行长度为signal的个数M，每一列的长度为所有signal中strategy最多的signal的strategy数N。每一行为 [Signal1.Signal1Strategy1, Signal2.Signal2Strategy1, ...],代表一连串来自不同指标的策略的组合（其可能意义为：k线穿过布林带中轨的同时，MACD出现金叉，stoch rsi也出现金叉等）。

这个引擎需要足够的对拓展开放，尽量不关注signal和strategy，param等内含业务逻辑的实现，无论我们怎么添加新的signal，新的strategy，都要允许即插即用。

### 5. 回测执行引擎

请参考以下代码：

```python

# 某Signal的strategy内部，前略
        df_long = df.filter(long_conditions)
        short_conditions = ...
        df_short = df.filter(short_conditions)
        return [
            {
                "name" : "Long",
                "start_time" : df_long["start_time"].to_list(),
                "datetime" : df_long["datetime"].to_list(),
                "marker" : dict(color='yellow', size=10)
            },
            {
                "name" : "Short",
                "start_time" : df_short["start_time"].to_list(),
                "datetime" : df_short["datetime"].to_list(),
                "marker" : dict(color='blue', size=10)
            }
        ]
# 后略

#==================================================================================#

# signal外部的回测程序，signal_backtest内部挂载了signal的实例，并且可以读取df，供内部的signal实例使用
res = signal_backtest("ETH-USDT", start_time="20250527", end_time="20250627", returns_interval = [10, 20, 30]) 
for d, r in res: LK.info(f"{d} return: {r}") #LK为logger类，已存在

```
如上代码，condition的用途是df.filter(condition)筛选出一些符合多/空买入条件的点。现在我需要你
1. 结合前面的内容，利用StrategyEngine返回的strategies里的strategy.get_condition得到的所有condition的组合（即，取与的结果），作为df.filter的条件
2. 允许自动化分阶段执行signal backtest：使用一个循环来替换标的"ETH-USDT"，遍历所有标的的回测，start time和end time改为一些固定的区间（如一个季度，一年，一轮牛市/熊市等），每一个循环只回测该区间时间内的所有标的，不测试其他区间。
3. 设计一个对接strategy组合的接口，允许拓展，功能为保存该strategy组合的所有参数与回测结果


## 三、输入输出

### 1. 输入依赖
以上所有信号、策略的计算均依赖交易数据的polars Dataframe，其格式如下：

```
"""
df:
┌─────────────────────┬─────────────────────┬────────────────┬────────────────┬───┬────────────────┬─────────────┬─────────────────┬────────────┐
│ start_time          ┆ end_time            ┆ open           ┆ high           ┆low┆ close          ┆ vol         ┆ money           ┆ trades_cnt │
│ ---                 ┆ ---                 ┆ ---            ┆ ---            ┆---┆ ---            ┆ ---         ┆ ---             ┆ ---        │
│ i64                 ┆ i64                 ┆ f64            ┆ f64            ┆f64┆ f64            ┆ f64         ┆ f64             ┆ i64        │
╞═════════════════════╪═════════════════════╪════════════════╪════════════════╪═══╪════════════════╪═════════════╪═════════════════╪════════════╡
"""
```
其中，每一行为一分钟的数据，并且使用文件名来区分标的，如ETH-USDT.parquet.0

### 2. 输出

输出所有条件下的回测实验的结果，并保存到log/文件里。第一阶段先使用log记录实验结果，格式可参考：
```
[2025-07-07 14:26:55.911] INFO[BollingerSignal.bollinger_bandwidth]: long conditions: [([(col("high").shift([dyn int: 1])) > (col("upper_band").shift([dyn int: 1]))].all_horizontal([[(col("high").shift([dyn int: 2])) > (col("upper_band").shift([dyn int: 2]))], [(col("high").shift([dyn int: 3])) > (col("upper_band").shift([dyn int: 3]))]])) & ([(col("bandwidth").shift([dyn int: 1])) < (col("bandwidth").shift([dyn int: 0]))].all_horizontal([[(col("bandwidth").shift([dyn int: 2])) < (col("bandwidth").shift([dyn int: 1]))], [(col("bandwidth").shift([dyn int: 3])) < (col("bandwidth").shift([dyn int: 2]))]]))] [in /home/<USER>/python-workspace/3rdMar/code/signal/bollinger.py:155]
[2025-07-07 14:26:55.911] INFO[BollingerSignal.bollinger_bandwidth]: short conditions: [([(col("low")) < (col("lower_band"))]) & ([(col("bandwidth").shift([dyn int: 1])) < (col("bandwidth").shift([dyn int: 0]))].all_horizontal([[(col("bandwidth").shift([dyn int: 2])) < (col("bandwidth").shift([dyn int: 1]))], [(col("bandwidth").shift([dyn int: 3])) < (col("bandwidth").shift([dyn int: 2]))]]))] [in /home/<USER>/python-workspace/3rdMar/code/signal/bollinger.py:156]
[2025-07-07 14:26:55.911] INFO[BollingerSignal.bollinger_bandwidth]: window:10, num_std:2, lookback:3, 10235/44640 long points, 10235/44640 short points [in /home/<USER>/python-workspace/3rdMar/code/signal/bollinger.py:157]
[2025-07-07 14:27:01.677] INFO[<module>]: Long return: shape: (1, 6)
┌─────────────────────┬─────────────────────┬─────────────────────┬────────────────────┬────────────────────┬────────────────────┐
│ long_return_10_mean ┆ long_return_20_mean ┆ long_return_30_mean ┆ long_return_10_var ┆ long_return_20_var ┆ long_return_30_var │
│ ---                 ┆ ---                 ┆ ---                 ┆ ---                ┆ ---                ┆ ---                │
│ f64                 ┆ f64                 ┆ f64                 ┆ f64                ┆ f64                ┆ f64                │
╞═════════════════════╪═════════════════════╪═════════════════════╪════════════════════╪════════════════════╪════════════════════╡
│ -0.000011           ┆ -0.000015           ┆ -0.000036           ┆ 0.00001            ┆ 0.000018           ┆ 0.000028           │
└─────────────────────┴─────────────────────┴─────────────────────┴────────────────────┴────────────────────┴────────────────────┘ [in /home/<USER>/python-workspace/3rdMar/code/signal/indicator.py:216]
[2025-07-07 14:27:01.677] INFO[<module>]: Short return: shape: (1, 6)
┌──────────────────────┬──────────────────────┬──────────────────────┬─────────────────────┬─────────────────────┬─────────────────────┐
│ short_return_10_mean ┆ short_return_20_mean ┆ short_return_30_mean ┆ short_return_10_var ┆ short_return_20_var ┆ short_return_30_var │
│ ---                  ┆ ---                  ┆ ---                  ┆ ---                 ┆ ---                 ┆ ---                 │
│ f64                  ┆ f64                  ┆ f64                  ┆ f64                 ┆ f64                 ┆ f64                 │
╞══════════════════════╪══════════════════════╪══════════════════════╪═════════════════════╪═════════════════════╪═════════════════════╡
│ 0.000021             ┆ 0.000033             ┆ 0.000064             ┆ 0.00001             ┆ 0.000019            ┆ 0.000028            │
└──────────────────────┴──────────────────────┴──────────────────────┴─────────────────────┴─────────────────────┴─────────────────────┘
```
在设计时请保留拓展的接口，以便日后可以输出到格式化的文件中。

## 四、注意事项

请你对以上的需求进行详细的分析，并给出一个整体设计方案。设计方案需要包括：

1. 系统架构设计
2. 各功能类ER关系（包括接口、抽象类）
3. 接口文档
4. 各功能需求说明（包括类之间互相调用的关系和顺序，类似状态机或者uml图的描述）

我在本文档中给出的例子仅供参考，你可以分析其中命名规则、架构设计是否合理，是否有多余、耦合的设计；是否有考虑不足的设计；

最后，如果可以的话，尽量输出结构化的文档，可以辅以伪代码demo进行说明，无需直接构建一个严格的工程。 
