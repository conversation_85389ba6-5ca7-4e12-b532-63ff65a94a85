──────────────────────────────  
# 《投资策略生成框架》总体架构设计  
──────────────────────────────  
版本：v0.1  
作者：Kimi  
日期：2025-07-19  

## 一、设计目标  
1. 把“信号(Signal)”与“策略(Strategy)”解耦，Strategy 是信号的可插拔最小执行单元。  
2. 把“参数(Parameter)”从业务逻辑中剥离，支持自动生成、注入与序列化。  
3. 通过 StrategyEngine 完成跨信号的策略组合遍历（笛卡尔积）。  
4. 通过 BacktestEngine 完成固定时间区间、全标的批量化回测。  
5. 所有结果可落盘，接口保持对后续持久化拓展开放。  

## 二、系统分层  
┌──────────────┐  
│  CLI / 调度  │  (可选，后续接入)  
├──────────────┤  
│  BacktestEngine │  → 遍历标的+区间 → 调用 StrategyEngine → 跑回测 → 写结果  
├──────────────┤  
│  StrategyEngine │  → 生成策略矩阵 → 注入参数 → 返回 StrategyBundle  
├──────────────┤  
│  Strategy层    │  BaseStrategy → 具体策略（如 BollBandwidthStrategy）  
├──────────────┤  
│  Signal层      │  BaseSignal → 仅做指标计算，不含入场条件  
├──────────────┤  
│  Parameter层   │  ParamDescriptor / ParamSpace → 自动生成参数网格  
├──────────────┤  
│  数据层        │  DataLoader → 按标的+区间 → polars.DataFrame  
└──────────────┘  

## 三、核心类图（简化 UML）  

```
+----------------+          +---------------------+
|  BaseSignal    |<>------|  BaseStrategy       |
+----------------+ 1   *  +---------------------+
| +calc(df): df  |        | +get_condition(df)  |
+----------------+        | +params: ParamSpace |
                          +----------+----------+
                                     ^
                        +------------+------------+
                        |                         |
            BollingerSignal              BollBandwidthStrategy
                        |                         |
                        +------------+------------+
                                     |
                               uses (组合信号)
```

## 四、接口与抽象类  

1. BaseSignal  
```python
class BaseSignal(ABC):
    param_space: ParamSpace #参数网格
    @abstractmethod
    def compute(self, df: pl.DataFrame, params: dict) -> pl.DataFrame:
        """只负责把指标算完并返回新的 df（含新增列），不生成信号"""
        pass
```

2. BaseStrategy  
```python
class BaseStrategy(ABC):
    signal_cls: type[BaseSignal]   # 所属信号
    param_space: ParamSpace        # 参数网格

    @abstractmethod
    def get_condition(self, df: pl.DataFrame, params: dict) -> tuple[pl.Expr, pl.Expr]:
        """返回 (long_condition, short_condition) 两个 polars.Expr"""
        pass
```

3. Parameter 体系  
```python
@dataclass
class ParamDescriptor:
    name: str
    type_: Literal['int', 'float', 'categorical']
    low: Any = None
    high: Any = None
    choices: list = None

class ParamSpace:
    def __init__(self, descriptors: list[ParamDescriptor]):
        self.desc = descriptors

    def grid(self) -> list[dict]:
        """返回 [{param_name:value}, ...] 固定步长网格"""
        ...
```

4. StrategyEngine  
```python
class StrategyEngine:
    def __init__(self):
        self.registry: dict[str, list[type[BaseStrategy]]] = defaultdict(list)

    def register(self, strategy_cls: type[BaseStrategy]):
        sig = strategy_cls.signal_cls.__name__
        self.registry[sig].append(strategy_cls)

    def generate(self) -> list[StrategyBundle]:
        """
        返回：list[StrategyBundle]
        每个 StrategyBundle 的成员数 = 当前跨 Signal 组合里 Signal 的个数
        --------------------------------
        三层笛卡尔积：
        ① 跨 Signal 的策略类组合
        ② 每个类组合内部
           – Signal 参数 × Strategy 参数 的网格
        ③ 再把所有成员的参数网格做整体积
        """
        from itertools import product

        # ------------------------------------------------------------------
        # ① class_matrix: list[tuple[type[BaseStrategy], ...]]
        #    长度 = 所有信号策略数的乘积
        #    例如 [ (BollBandwidth, MACDGolden), (BollCrossMid, MACDGolden), ... ]
        # ------------------------------------------------------------------
        class_matrix = product(*self.registry.values())

        bundles = []
        for class_combo in class_matrix:   # 遍历每一条跨 Signal 的“策略类组合”
            # skeleton: list[tuple[type[BaseSignal], type[BaseStrategy]]]
            # 长度 = len(class_combo) = 当前组合里的 Signal 数量
            skeleton = [(strat_cls.signal_cls, strat_cls) for strat_cls in class_combo]

            # ------------------------------------------------------------------
            # ② param_grids: list[iterator[tuple[dict, dict]]]
            #    每个元素是 (sig_params, strat_params) 的迭代器
            # ------------------------------------------------------------------
            param_grids = [
                product(sig_cls.param_space.grid(), strat_cls.param_space.grid())
                for sig_cls, strat_cls in skeleton
            ]

            # ------------------------------------------------------------------
            # ③ product(*param_grids) 生成器
            #    每个元素 = tuple[tuple[dict, dict], ...]
            #    长度 = 所有成员参数网格大小的乘积
            # ------------------------------------------------------------------
            for param_combo in product(*param_grids):
                # param_combo 的形状示例：
                # ((sig_p1, strat_p1), (sig_p2, strat_p2), ...)
                members = [
                    (sig_cls, strat_cls, sig_p, strat_p)
                    for (sig_cls, strat_cls), (sig_p, strat_p) in zip(skeleton, param_combo)
                ]
                bundles.append(StrategyBundle(members))
        return bundles
```

5. StrategyBundle  
```python
@dataclass
class StrategyBundle:
    # 一个 bundle 只对应 1 个 Signal + 1 个 Strategy
    signal_cls: type[BaseSignal]
    strategy_cls: type[BaseStrategy]
    signal_params: dict     # 仅 Signal 的参数
    strategy_params: dict   # 仅 Strategy 的参数
    params: {"signal": signal_params,
            "strategy": strategy_params}

    def uid(self) -> str:
        return hashlib.md5(
            json.dumps([self.signal_cls.__name__,
                        self.strategy_cls.__name__,
                        self.signal_params,
                        self.strategy_params]).encode()
        ).hexdigest()[:8]
    @classmethod
    def from_classes(cls, sig_cls, strat_cls):
        for sig_p, strat_p in product(sig_cls.param_space.grid(),
                                    strat_cls.param_space.grid()):
            yield cls(sig_cls, strat_cls, sig_p, strat_p)
```

6. BacktestEngine  
```python
class BacktestEngine:
    def __init__(self, data_root: Path, intervals: list[int], logger):
        self.data_root = data_root
        self.intervals = intervals
        self.logger = logger

    def run(self,
            bundle: StrategyBundle,
            symbol: str,
            start: datetime,
            end: datetime) -> dict:
        df = DataLoader.load(self.data_root, symbol, start, end)
        long_cond = pl.lit(True)
        short_cond = pl.lit(True)

        for strategy, params in zip(bundle.strategies, bundle.params):
            # l, s = strategy.get_condition(df, params)
            df = bundle.signal_cls().compute(raw_df, params["signal"])
            l, s = bundle.strategy_cls().get_condition(df, params["strategy"])
            long_cond &= l
            short_cond &= s
            


        df_long = df.filter(long_cond)
        df_short = df.filter(short_cond)

        returns = self._calc_returns(df_long, df_short)
        return {
            "bundle_id": bundle.uid(),
            "symbol": symbol,
            "start": start,
            "end": end,
            "params": bundle.params,
            "returns": returns
        }

    def _calc_returns(self, df_long, df_short) -> dict:
        # 根据需求计算 10/20/30 min 收益等
        ...
```

7. DataLoader  
```python
class DataLoader:
    @staticmethod
    def load(root: Path, symbol: str, start: datetime, end: datetime) -> pl.DataFrame:
        file = root / f"{symbol}.parquet.0"
        df = pl.read_parquet(file)
        mask = (pl.col("start_time") >= int(start.timestamp()*1000)) & \
               (pl.col("end_time") <= int(end.timestamp()*1000))
        return df.filter(mask)
```

## 五、运行流程（状态机描述）  

```
(=============有问题=================)
初始化
  │
  ├─ StrategyEngine.register(所有Strategy类)
  │
  ├─ StrategyEngine.generate()  -> list[StrategyBundle]
  │
  ├─ for bundle in bundles:
  │     for symbol in universe:
  │         for interval in periods:
  │             BacktestEngine.run(bundle, symbol, interval.start, interval.end)
  │                 │
  │                 ├─ 记录 logger.info(...)
  │                 └─ 后续可写入 CSV / Parquet / DB
```

## 六、伪代码 Demo  

```python
# --- 1. 注册所有策略 --------------------------------
engine = StrategyEngine()
engine.register(BollBandwidthStrategy)
engine.register(BollCrossMidStrategy)
engine.register(MACDGoldenStrategy)   # 假设已实现

# --- 2. 生成策略矩阵 --------------------------------

final_bundles = engine.generate()          
# --- 3. 回测 ----------------------------------------
bt = BacktestEngine(Path("/data/klines"), [10, 20, 30], LK)

for bundle in final_bundles:
    for symbol in ["ETH-USDT", "BTC-USDT"]:
        for start, end in QUARTERS_2024:
            res = bt.run(bundle, symbol, start, end)
            LK.info(f"{res['bundle_id']} {symbol} {start}~{end} {res['returns']}")
```

## 七、扩展点  

1. 持久化  
   实现 `ResultWriter` 接口：  
   ```
   class ResultWriter(Protocol):
       def write(self, res: dict) -> None
   ```
   后续可接入 `CsvWriter`, `ParquetWriter`, `InfluxWriter` 等。  

2. 参数搜索  
   将 ParamSpace.grid() 替换为随机搜索、贝叶斯优化、Optuna 等。  

3. 并行化  
   BacktestEngine.run 内部使用 `concurrent.futures.ProcessPoolExecutor` 即可横向扩展。  

4. CLI  
   使用 typer / click 包装上述流程，实现命令行一键批量跑。  

## 八、命名与目录建议  

```
quant/
├── signals/
│   ├── __init__.py
│   ├── base.py
│   └── bollinger.py
├── strategies/
│   ├── __init__.py
│   ├── base.py
│   └── bollinger_bandwidth.py
├── engine/
│   ├── strategy_engine.py
│   └── backtest_engine.py
├── param/
│   └── space.py
├── loader.py
└── main.py
```

──────────────────────────────  
至此，架构设计完毕。

────────────────────  
# ParamSpace 在 Signal ↔ Strategy 中的落地细节  
────────────────────  

## 一、ParamSpace 的职责再回顾  
1. 描述**可调参数**的元信息（名称、类型、取值范围）。  
2. 提供**生成器**，产出 dict 形式的参数组合，供外部无差异注入。  
3. 与业务逻辑（Signal/Strategy 计算细节）彻底解耦——它只关心“有哪些参数、如何枚举”。  

## 二、三处关键落地点  
A. Signal 的纯指标计算参数  
B. Strategy 的入场条件参数  
C. StrategyEngine 组装时把 A+B 合并成一条“完整参数向量”  

下面逐点展开。  

────────────────────  
### A. Signal 侧的参数  

1. 定义  
```python
class BollingerSignal(BaseSignal):
    param_space = ParamSpace([
        ParamDescriptor("window", "int", low=10, high=50),
        ParamDescriptor("num_std", "float", low=1.0, high=3.0, step=0.5),
    ])

    def compute(self, df: pl.DataFrame, params: dict) -> pl.DataFrame:
        w = params["window"]
        n = params["num_std"]
        # 用 w,n 计算布林带 …
        return df
```  
- Signal 只声明**自己需要**的参数；不会关心 Strategy 要什么。  
- `compute` 的第二个形参 `params` 由外部注入。  

2. 生成方式  
```python
for sig_params in BollingerSignal.param_space.grid():
    df_enriched = BollingerSignal().compute(raw_df, sig_params)
```

────────────────────  
### B. Strategy 侧的参数  

1. 定义  
```python
class BollBandwidthStrategy(BaseStrategy):
    signal_cls = BollingerSignal
    param_space = ParamSpace([
        ParamDescriptor("lookback", "int", low=2, high=5),
        ParamDescriptor("tolerance", "int", low=10, high=50),
    ])

    def get_condition(self, df: pl.DataFrame, params: dict) -> tuple[pl.Expr, pl.Expr]:
        lb = params["lookback"]
        tol = params["tolerance"]
        # 用 df 中已由 Signal 计算好的列 + lb,tol 拼出条件表达式
        ...
```  

2. 关键约定  
- Strategy 的参数空间里**不包含 Signal 的参数**；它假设 df 已经包含所需列。  
- 因此 Strategy 与 Signal 的 param_space 彼此独立，天然解耦。  

────────────────────  
C. 合并：StrategyEngine 如何一次性拿到“完整参数向量”  

1. 每个 Strategy 实例化时需要知道：  
   (1) 自己那一份参数 `strat_params`  
   (2) 对应 Signal 那一份参数 `sig_params`  

2. 组合规则  
```
完整参数 = sig_params ∪ strat_params
```  
为了避免 key 冲突，我们采用**两层命名空间**的扁平 dict：  
```python
{
    "signal.window": 20,
    "signal.num_std": 2.0,
    "strategy.lookback": 3,
    "strategy.tolerance": 30,
}
```  
注：  
- 前缀 `signal.` / `strategy.` 由 StrategyEngine 在组合时自动加上。  
- 真正注入给 Signal/Strategy 时再剥掉前缀即可。  

3. StrategyBundle 的构造伪码  
```python
class StrategyBundle:
    ...
    @classmethod
    def from_classes(cls, sig_cls, strat_cls):
        for sig_p, strat_p in product(sig_cls.param_space.grid(),
                                      strat_cls.param_space.grid()):
            full = {f"signal.{k}": v for k, v in sig_p.items()}
            full.update({f"strategy.{k}": v for k, v in strat_p.items()})
            yield cls(strat_cls, full)
```  

4. 运行时注入  
BacktestEngine 在调用时：  
```python
sig_params = {k[7:]: v for k, v in bundle.params.items() if k.startswith("signal.")}
strat_params = {k[9:]: v for k, v in bundle.params.items() if k.startswith("strategy.")}

df = signal_cls().compute(raw_df, sig_params)
long, short = strategy_cls().get_condition(df, strat_params)
```

────────────────────  
## 四、完整示例（一条参数链）  

1. 声明  
- Signal：BollingerSignal 有 `window`, `num_std`  
- Strategy：BollBandwidthStrategy 有 `lookback`, `tolerance`  

2. ParamSpace 产出  
```
sig_grid  = [{'window':20,'num_std':2}, {'window':30,'num_std':2.5}]
strat_grid= [{'lookback':3,'tolerance':30}, {'lookback':5,'tolerance':40}]
```  

3. StrategyEngine 组合后得到 2×2 = 4 条完整参数  
```
1) {'signal.window':20,'signal.num_std':2,'strategy.lookback':3,'strategy.tolerance':30}
2) {'signal.window':20,'signal.num_std':2,'strategy.lookback':5,'strategy.tolerance':40}
3) {'signal.window':30,'signal.num_std':2.5,'strategy.lookback':3,'strategy.tolerance':30}
4) {'signal.window':30,'signal.num_std':2.5,'strategy.lookback':5,'strategy.tolerance':40}
```  

4. BacktestEngine 逐条注入并回测。  

────────────────────  
## 五、常见疑问解答  

Q1：如果将来一个 Strategy 需要同时依赖 **两个** Signal 的指标怎么办？  
→ 把 Signal 的粒度拆细或引入“复合信号”概念；ParamSpace 仍按上述两层命名空间，只是  
`signal1.xxx`, `signal2.xxx`, `strategy.xxx` 三段前缀即可。  

Q2：参数很多导致笛卡尔积爆炸？  
→ ParamSpace 除 `.grid()` 外再提供 `.random(n)`、`.bayesian()` 等生成器，接口保持一致。  

Q3：Signal 与 Strategy 之间想共享某些计算结果以减少重复？  
→ 可以在 BacktestEngine 内做“计算缓存”：  
   key = hash((signal_cls.__name__, sig_params_tuple)) → DataFrame。  

────────────────────  
至此，ParamSpace 在 Signal ↔ Strategy 中的完整落地路径已说明。

# ParameterExpander
你问到了关键点。  
**StrategyEngine.generate() 目前只把“策略类”排列组合起来，参数维度并未展开**。  
也就是说：

- 每个 `StrategyBundle` 里目前只有 **“类”**（`signal_cls`, `strategy_cls`），没有 **任何实例化参数**。  
- Signal 的参数 × Strategy 的参数 的 **笛卡尔积尚未发生**。

因此：

| 维度 | StrategyEngine 生成的 bundle 里 | 还需要什么 |
|------|---------------------------------|-------------|
| Signal 选择 | ✅ 已固定（每个 bundle 只含 1 个 Signal） | |
| Strategy 选择 | ✅ 已固定（每个 bundle 只含 1 个 Strategy 类） | |
| Signal 参数 | ❌ 缺失 | 需要再枚举 |
| Strategy 参数 | ❌ 缺失 | 需要再枚举 |

────────────────────  
## 什么时候/在哪里展开参数？

1. 在 **StrategyEngine.generate()** 之后，再套一层 **参数展开器**（ParameterExpander）。  
   伪代码示例：

```python
class ParameterExpander:
    @staticmethod
    def expand(bundle: StrategyBundle) -> list[StrategyBundle]:
        """
        把 1 个只含“类”的 bundle 炸成 N×M 个含具体参数的 bundle
        """
        sig_cls = bundle.signal_cls
        strat_cls = bundle.strategy_cls
        bundles = []
        for sig_p, strat_p in product(
                sig_cls.param_space.grid(),
                strat_cls.param_space.grid()):
            bundles.append(
                StrategyBundle(
                    signal_cls=sig_cls,
                    strategy_cls=strat_cls,
                    signal_params=sig_p,
                    strategy_params=strat_p
                )
            )
        return bundles
```

2. 完整调用链：

```python
engine = StrategyEngine()
engine.register(BollBandwidthStrategy)
engine.register(MACDGoldenStrategy)

raw_bundles = engine.generate()           # 仅类组合
final_bundles = []
for rb in raw_bundles:
    final_bundles.extend(ParameterExpander.expand(rb))
```

3. 之后 `final_bundles` 才真正含有 **Signal 参数 × Strategy 参数** 的全部实例，可以交给 BacktestEngine。

────────────────────  
一句话总结  
StrategyEngine 只负责 **“类”** 的组合；  
真正把 **Signal 参数 × Strategy 参数** 炸开的工作，要么用 `from_classes`（单 Signal 场景），要么用额外的 `ParameterExpander`（跨 Signal 多策略场景）。

# 系统设计UML

```plantuml
@startuml
' ===== 0. 通用 / 外部 =====
interface ResultWriter <<interface>> {
    +write(res: dict): void
}

class LK <<logger>> {
    +info(msg: str)
}

' ===== 1. 数据层 =====
class DataLoader {
    +{static} load(root: Path, symbol: str, start: datetime, end: datetime): pl.DataFrame
}

' ===== 2. 参数层 =====
class ParamDescriptor {
    +name: str
    +type_: "int" | "float" | "categorical"
    +low: Any
    +high: Any
    +choices: list
}

class ParamSpace {
    +descriptors: list<ParamDescriptor>
    +grid(): list<dict>
    +random(n: int): list<dict>
}

' ===== 3. 信号层 =====
abstract class BaseSignal {
    +{abstract} compute(df: pl.DataFrame, params: dict): pl.DataFrame
    +param_space: ParamSpace
}

class BollingerSignal {
    +compute(df: pl.DataFrame, params: dict): pl.DataFrame
}

' ===== 4. 策略层 =====
abstract class BaseStrategy {
    +{abstract} get_condition(df: pl.DataFrame, params: dict): tuple<pl.Expr, pl.Expr>
    +signal_cls: type<BaseSignal>
    +param_space: ParamSpace
}

class BollBandwidthStrategy {
    +signal_cls = BollingerSignal
    +get_condition(df: pl.DataFrame, params: dict): tuple<pl.Expr, pl.Expr>
}

class BollCrossMidStrategy {
    +signal_cls = BollingerSignal
    +get_condition(df: pl.DataFrame, params: dict): tuple<pl.Expr, pl.Expr>
}

' ===== 5. 引擎层 =====
class StrategyBundle {
    +signal_cls: type<BaseSignal>
    +strategy_cls: type<BaseStrategy>
    +signal_params: dict
    +strategy_params: dict
    +uid(): str
}

class StrategyEngine {
    -registry: dict<str, list<type<BaseStrategy>>>
    +register(strategy_cls: type<BaseStrategy>)
    +generate(): list<StrategyBundle>
}

class ParameterExpander {
    +{static} expand(bundle: StrategyBundle): list<StrategyBundle>
}

class BacktestEngine {
    -data_root: Path
    -intervals: list<int>
    -logger: LK
    +run(bundle: StrategyBundle, symbol: str, start: datetime, end: datetime): dict
}

' ===== 6. CLI / 调度（占位） =====
class MainScheduler {
    +main()
}

' ===== 继承关系 =====
BaseSignal       <|-- BollingerSignal
BaseStrategy     <|-- BollBandwidthStrategy
BaseStrategy     <|-- BollCrossMidStrategy

' ===== 组合 / 聚合 / 关联 =====
BaseSignal       *-- ParamSpace
BaseStrategy     *-- ParamSpace
ParamSpace       o-- ParamDescriptor

StrategyEngine   o-- BaseStrategy   : registers >

StrategyBundle   --> BaseSignal     : derived from .signal_cls
StrategyBundle   --> BaseStrategy   : derived from .strategy_cls

ParameterExpander ..> StrategyBundle : creates >

StrategyEngine   ..> ParameterExpander : uses >
MainScheduler    ..> StrategyEngine    : uses >
MainScheduler    ..> ParameterExpander : uses >
MainScheduler    ..> BacktestEngine    : uses >
BacktestEngine   ..> DataLoader        : uses >
BacktestEngine   ..> ResultWriter      : uses >
BacktestEngine   ..> LK                : uses >

ResultWriter     <|.. CsvWriter
ResultWriter     <|.. ParquetWriter
@enduml
```
```
符号​​	​​关系类型​​	​​语义说明​​	​​示例代码​​	​​图示说明​​
-->	​​单向关联​​	一个类持有另一个类的引用（如成员变量）。	ClassA --> ClassB	实线箭头（→）
..>	​​依赖​​	临时性使用（如方法参数、局部变量），耦合度最弱。	ClassA ..> ClassB	虚线箭头（⤍）
*--	​​组合​​	强整体-部分关系，部分不能脱离整体存在（如“头与嘴”）。	Car *-- Engine	实线+实心菱形（◆─）
o--	​​聚合​​	弱整体-部分关系，部分可独立存在（如“学校与老师”）。	School o-- Teacher	实线+空心菱形（◇─）
<|..​实现​​	   类实现接口（接口方法必须全部实现）。	ResultWriter <|.. CsvWriter
```

# 细化UML
```plantuml
@startuml
' ===== 0. 通用 / 外部 =====
class LK <<logger>> {
    +info(msg: str)
}

' ===== 1. 数据层 =====
class DataLoader {
    +{static} load(root: Path, symbol: str, start: datetime, end: datetime): pl.DataFrame
}

' ===== 2. 参数层 =====
class ParamDescriptor {
    +name: str
    +type_: "int" | "float" | "categorical"
    +low: Any
    +high: Any
    +choices: list
}

class ParamSpace {
    +descriptors: list<ParamDescriptor>
    +grid(): list<dict>
    +random(n: int): list<dict>
}


' ===== 3. 信号层 =====
abstract class BaseIndicator {
    +param_space: ParamSpace
    +{abstract} compute(df: pl.DataFrame, freq:str): dict[str, pl.Expr]
}

class BollingerIndicator <<singleton>> {
    +compute(df: pl.DataFrame, freq:str): dict[str, pl.Expr]
}

' ===== 4. 信号层 =====
abstract class BaseSignal {
    +param_space: ParamSpace
    +{abstract} expr(df: pl.DataFrame): dict[str, pl.Expr]
    +save() '保存结果数据
}

class LeafSignal {
    +indicator_cls: type<BaseIndicator>
    +expr(df: pl.DataFrame): dict[str, pl.Expr]
}

class BollBandwidthSignal {
    +indicator_cls: BollingerIndicator
    +expr(df: pl.DataFrame): dict[str, pl.Expr]
}

class CompositeSignal {
    +uid(): str
    +children: list[BaseSignal]
    +expr(df: pl.DataFrame): dict[str, pl.Expr]
    +export() '支持导出为可立即装载的signal
}


' ===== 5. 引擎层 =====
class SignalEngine {
    -registry: dict[str, list[type<BaseSignal>]]
    -expand(comps:CompositeSignal): list[CompositeSignal] ' 展开所有参数
    +register(strategy_cls: type<BaseSignal>)
    +generate(): list[CompositeSignal] ' 生成不同组合方式（operator）
}

class BacktestEngine {
    -data_root: Path
    -intervals: list<int>
    -logger: LK
    +run(bundle: StrategyBundle, symbol: str, start: datetime, end: datetime): dict
}

' ===== 6. CLI / 调度（占位） =====
class MainScheduler {
    +main()
}

' ===== 继承关系 =====
BaseIndicator       <|-- BollingerIndicator
BaseSignal     <|-- LeafSignal
BaseSignal     <|-- CompositeSignal
LeafSignal     <|-- BollBandwidthSignal : is-a 


' ===== 组合 / 聚合 / 关联 =====
BaseSignal       *-- ParamSpace
BaseIndicator     *-- ParamSpace
ParamSpace       o-- ParamDescriptor

SignalEngine   o-- LeafSignal   : registers >
SignalEngine   o-- CompositeSignal   : generates >
CompositeSignal ..> BaseSignal : dependency >
LeafSignal o--> BaseIndicator

MainScheduler    ..> SignalEngine    : uses >
MainScheduler    ..> BacktestEngine    : uses >
BacktestEngine   ..> DataLoader        : uses >
BacktestEngine   ..> LK                : uses >
BaseSignal   ..> LK                : uses >
@enduml
```

```yaml
BollingerIndicator: #indicator
    params:
        type: grid #参数生成方式
        window: int,[10,30],10 #参数类型，[初始值，结束值]，生成网格步长
        num_std: float,[1,3],0.5

BollingerCrossMidSignal: #leafsignal
    indicator: BollingerIndicator
    params:
        lookback: int,[3,9],2

FvgIndicator:
    params: 
        type: grid
        min_gap_rate: float,[0.0005,0.03],0.005

FvgRtnSignal: #leafsignal
    indicator: FvgIndicator
    params:
        type: grid
        threshold: float,[0.001,0.005],0.001

CompositeSignal: #compositesignal
    children: BollingerCrossMidSignal,FvgRtnSignal
    params:
        threshold: float,[0.005,0.02],0.005
        operator: #支持的组合方式
            +: 
                constrait: sum(weights)=1,weights<=1
                weights: float,0.5,0.5 #权重参数类型，w1初始值，w2初始值
                step: 0.1 #双向生成步长
            /*: 
                constrait: prod(weights)=1
                weights: float,0.5,2
                step: 0.1
            is not: null
        ...
```

"""
┌─────────────────────┬─────────────────────┬────────────────┬────────────────┬───┬────────────────┬─────────────┬─────────────────┬────────────┐
│ start_time          ┆ end_time            ┆ open           ┆ high           ┆ … ┆ close          ┆ vol         ┆ money           ┆ trades_cnt │
│ ---                 ┆ ---                 ┆ ---            ┆ ---            ┆   ┆ ---            ┆ ---         ┆ ---             ┆ ---        │
│ i64                 ┆ i64                 ┆ str            ┆ str            ┆   ┆ str            ┆ str         ┆ str             ┆ i64        │
╞═════════════════════╪═════════════════════╪════════════════╪════════════════╪═══╪════════════════╪═════════════╪═════════════════╪════════════╡
"""