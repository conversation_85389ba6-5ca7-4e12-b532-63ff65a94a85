好的，这是一个非常清晰和有价值的需求。现有的框架虽然能工作，但在可扩展性、可组合性和解耦方面有很大的提升空间。你提出的抽象“原语”和“生成式引擎”的想法是构建一个健壮框架的核心。

我将根据你的需求，提供一个详细的系统设计方案，包括架构、类设计、接口和工作流程。

---

## 投资策略生成框架设计方案

### 一、 系统架构设计

我们设计的核心思想是**关注点分离 (Separation of Concerns)**。系统将拆分为以下几个独立但协同工作的核心模块：

1.  **参数 (Parameters)**: 使用类型化的数据类来定义和管理所有可调参数，与逻辑代码解耦。
2.  **指标 (Indicators)**: 负责在 `polars.DataFrame` 上计算技术指标，并添加为新的列。它不包含任何交易逻辑。
3.  **策略原语 (Strategy Primitives)**: 最小的逻辑单元。它定义了基于一个或多个指标列的入场条件，并返回一个 `polars.Expression`，而不是一个过滤后的 DataFrame。这是实现组合的关键。
4.  **策略族 (Strategy Families)**: 一个逻辑上的分组，将一个指标和多个基于该指标的策略原语关联起来。这对应你提到的 "Signal" 概念。
5.  **组合引擎 (Combination Engine)**: 负责将来自不同策略族的策略原语进行排列组合，生成最终的组合策略。
6.  **回测引擎 (Backtest Engine)**: 接收一个组合策略（即一个聚合后的 `polars.Expression`）、数据和回测配置，执行回测并计算结果。
7.  **结果处理器 (Result Handler)**: 负责记录和保存每一次回测的全部信息：策略组合、参数、条件表达式和回测结果。
8.  **执行器 (Orchestrator)**: 最高层控制器，负责协调以上所有模块，驱动整个回测流程（遍历交易对、时间周期、策略组合）。



### 二、 功能类设计与ER关系

我们将使用抽象基类（ABC）来定义核心接口，确保所有实现都遵循统一的规范。

#### 1. 参数类 (`parameters.py`)

使用 `dataclasses` 来创建结构化、类型化的参数容器。

```python
# a. 抽象基类 (可选，但推荐)
from dataclasses import dataclass

@dataclass
class BaseParameterSet:
    """参数集的基类"""
    def __str__(self):
        return ", ".join(f"{k}={v}" for k, v in self.__dict__.items())

# b. 具体实现
@dataclass
class BollingerBandsParams(BaseParameterSet):
    """布林带指标的参数"""
    window: int = 20
    num_std: float = 2.0

@dataclass
class BandwidthStrategyParams(BaseParameterSet):
    """布林带宽度策略的参数"""
    lookback: int = 3
```

**设计 rationale:**
*   **解耦**: 将参数从函数签名中移出，变为可配置的对象。
*   **类型安全**: `dataclasses` 提供类型提示，减少错误。
*   **易于序列化**: 方便地记录和复现实验。

#### 2. 指标类 (`indicators/base.py`, `indicators/bollinger.py`)

指标类的唯一职责是计算并添加列。

```python
# a. 抽象基类
import polars as pl
from abc import ABC, abstractmethod

class BaseIndicator(ABC):
    def __init__(self, params: BaseParameterSet):
        self.params = params

    @abstractmethod
    def calculate(self, df: pl.DataFrame) -> pl.DataFrame:
        """计算指标并将结果列添加到DataFrame中。"""
        raise NotImplementedError
        
    def get_required_columns(self) -> list[str]:
        """返回该指标计算所需的列名"""
        return ['close'] # 默认

# b. 具体实现
from .base import BaseIndicator
from ..parameters import BollingerBandsParams

class BollingerBandsIndicator(BaseIndicator):
    def __init__(self, params: BollingerBandsParams):
        super().__init__(params)

    def calculate(self, df: pl.DataFrame) -> pl.DataFrame:
        df = df.sort("start_time")
        
        middle = pl.col("close").rolling_mean(window_size=self.params.window)
        std_dev = pl.col("close").rolling_std(window_size=self.params.window)
        upper = middle + (std_dev * self.params.num_std)
        lower = middle - (std_dev * self.params.num_std)
        
        return df.with_columns(
            middle.alias("bb_middle"),
            upper.alias("bb_upper"),
            lower.alias("bb_lower"),
            ((upper - lower) / middle).alias("bb_bandwidth")
        )
```
**设计 rationale:**
*   **单一职责**: `BollingerBandsIndicator` 只负责计算布林带，不关心如何使用它。
*   **可复用**: 任何策略都可以使用这个计算出的指标列。
*   **性能**: 一次计算，多处使用。回测引擎可以缓存已计算的指标，避免重复计算。

#### 3. 策略原语类 (`strategies/base.py`, `strategies/bollinger.py`)

策略原语返回 `polars.Expression`，这是实现灵活组合的核心。

```python
# a. 抽象基类
import polars as pl
from abc import ABC, abstractmethod
from typing import Tuple

class BaseStrategy(ABC):
    def __init__(self, params: BaseParameterSet):
        self.params = params
        self.name = self.__class__.__name__

    @abstractmethod
    def get_conditions(self) -> Tuple[pl.Expr, pl.Expr]:
        """
        返回一个元组，包含做多和做空的 Polars 表达式。
        (long_condition_expr, short_condition_expr)
        如果某个方向没有条件，则返回 pl.lit(False)。
        """
        raise NotImplementedError

# b. 具体实现
from .base import BaseStrategy
from ..parameters import BandwidthStrategyParams

class BandwidthNarrowingStrategy(BaseStrategy):
    def __init__(self, params: BandwidthStrategyParams):
        super().__init__(params)

    def get_conditions(self) -> Tuple[pl.Expr, pl.Expr]:
        # 带宽连续N个周期收缩
        bandwidth_shrinking = pl.all_horizontal(
            pl.col("bb_bandwidth").shift(i) < pl.col("bb_bandwidth").shift(i - 1)
            for i in range(1, self.params.lookback + 1)
        )
        
        # 做多条件：带宽收缩 + 价格突破上轨
        long_cond = bandwidth_shrinking & (pl.col("high") > pl.col("bb_upper"))
        
        # 做空条件：带宽收缩 + 价格跌破下轨
        short_cond = bandwidth_shrinking & (pl.col("low") < pl.col("bb_lower"))
        
        return long_cond, short_cond

class MidBandCrossStrategy(BaseStrategy):
    # ... 其他策略实现 ...
    def get_conditions(self) -> Tuple[pl.Expr, pl.Expr]:
        # 价格从下方向上穿越中轨
        long_cond = (pl.col("close").shift(1) < pl.col("bb_middle").shift(1)) & \
                    (pl.col("close") > pl.col("bb_middle"))
        
        # 价格从上方向下穿越中轨
        short_cond = (pl.col("close").shift(1) > pl.col("bb_middle").shift(1)) & \
                     (pl.col("close") < pl.col("bb_middle"))
                     
        return long_cond, short_cond
```
**设计 rationale:**
*   **延迟执行**: 返回表达式而不是数据，允许 Polars 的查询优化器在最后一步高效地执行整个逻辑。
*   **可组合性**: `polars.Expression` 可以通过 `&` (与), `|` (或), `~` (非) 操作符进行无限组合。
*   **清晰**: 策略逻辑被清晰地封装在 `get_conditions` 方法中。

#### 4. 策略族与组合引擎 (`engine/combination_engine.py`)

引擎负责发现、组织和组合策略。

```python
from typing import List, Dict, Type, Iterable
from itertools import product
from ..indicators.base import BaseIndicator
from ..strategies.base import BaseStrategy

class StrategyFamily:
    """一个策略族，包含一个指标和多个相关策略。"""
    def __init__(self, name: str, indicator: BaseIndicator, strategies: List[BaseStrategy]):
        self.name = name
        self.indicator = indicator
        self.strategies = strategies

class CombinationEngine:
    def __init__(self):
        self.families: List[StrategyFamily] = []

    def register_family(self, family: StrategyFamily):
        """注册一个策略族。"""
        self.families.append(family)

    def generate_combinations(self) -> Iterable[List[BaseStrategy]]:
        """
        生成所有可能的策略组合。
        每个组合从每个已注册的策略族中选择一个策略。
        """
        if not self.families:
            return []
            
        # 提取每个策略族中的策略列表
        strategy_lists = [family.strategies for family in self.families]
        
        # 使用itertools.product计算笛卡尔积
        for combo in product(*strategy_lists):
            yield list(combo)
            
    def get_all_indicators(self) -> List[BaseIndicator]:
        """获取所有已注册策略族需要的指标实例。"""
        # 使用字典去重，确保每个指标只计算一次
        return list({family.indicator.__class__: family.indicator for family in self.families}.values())
```
**设计 rationale:**
*   **可扩展性 (即插即用)**: 只需要创建新的 `StrategyFamily` 并调用 `register_family` 即可将其纳入组合生成。引擎本身无需任何修改。
*   **逻辑清晰**: `StrategyFamily` 的概念很好地对应了你的 "Signal" 思想——一个指标及其派生的多种策略。
*   **高效组合**: `itertools.product` 是生成笛卡尔积的标准、高效方式。

#### 5. 回测引擎 (`engine/backtest_engine.py`)

回测引擎是执行计算和评估的核心。

```python
import polars as pl
from typing import List
from ..strategies.base import BaseStrategy

class BacktestEngine:
    def __init__(self, returns_intervals: List[int] = [10, 20, 30]):
        self.returns_intervals = returns_intervals

    def _calculate_returns(self, df: pl.DataFrame, entry_points_df: pl.DataFrame, side: str) -> pl.DataFrame:
        """计算未来N个周期的回报率。"""
        # 这是一个简化的回报计算，实际可能更复杂
        # df 包含所有价格数据, entry_points_df 包含入场点
        if entry_points_df.is_empty():
            return pl.DataFrame() # 返回空DataFrame
            
        # 将入场点与全量数据连接，以获取未来的价格
        merged = entry_points_df.join(df.select(["start_time", "close"]), on="start_time", how="left")
        
        return_exprs = []
        for interval in self.returns_intervals:
            future_close = pl.col("close").shift(-interval)
            # 做多：(卖出价 - 买入价) / 买入价
            # 做空：(买入价 - 卖出价) / 卖出价 = - (多头回报)
            ret = (future_close - pl.col("close")) / pl.col("close")
            if side == "short":
                ret = -ret
            return_exprs.append(ret.alias(f"{side}_return_{interval}"))
            
        returns_df = df.with_columns(return_exprs).select(pl.col(f"^{side}_return_\\d+$")).filter(pl.col(f"{side}_return_{self.returns_intervals[0]}").is_not_null())
        
        # 在这里计算均值、方差等统计数据
        # ... 此处为简化逻辑，实际应在entry_points上计算 ...
        # 正确的逻辑应是在入场点上计算回报，此处为伪代码示意
        
        # 返回统计结果
        stats_exprs = []
        for interval in self.returns_intervals:
             stats_exprs.append(pl.mean(f"{side}_return_{interval}").alias(f"{side}_return_{interval}_mean"))
             stats_exprs.append(pl.var(f"{side}_return_{interval}").alias(f"{side}_return_{interval}_var"))
             
        # 此处简化：假设 entry_points_df 包含了回报列
        # result_stats = entry_points_df.select(stats_exprs)
        # return result_stats
        
        # 简化版返回：
        print(f"Found {len(entry_points_df)} {side} entries.")
        return entry_points_df # 实际应返回回报统计结果

    def run(self, df: pl.DataFrame, combination: List[BaseStrategy]):
        # 1. 聚合所有策略的条件
        long_conditions = [strat.get_conditions()[0] for strat in combination]
        short_conditions = [strat.get_conditions()[1] for strat in combination]

        # 使用 & 运算符聚合所有条件
        final_long_cond = pl.all_horizontal(long_conditions)
        final_short_cond = pl.all_horizontal(short_conditions)

        # 2. 筛选入场点
        df_long_entries = df.filter(final_long_cond)
        df_short_entries = df.filter(final_short_cond)
        
        # 3. 计算回报（这里需要一个更复杂的回报计算模块）
        # 此处调用一个假设的、更健壮的回报计算函数
        long_results = self._calculate_returns(df, df_long_entries, "long")
        short_results = self._calculate_returns(df, df_short_entries, "short")

        return {
            "long_condition_str": str(final_long_cond),
            "short_condition_str": str(final_short_cond),
            "long_entries_count": len(df_long_entries),
            "short_entries_count": len(df_short_entries),
            "long_results": long_results,
            "short_results": short_results,
        }
```

### 三、 接口文档（简版）

-   `BaseParameterSet`: 数据类，用于存储参数。无方法。
-   `BaseIndicator.calculate(df)`: 输入原始`df`，返回增加了指标列的`df`。
-   `BaseStrategy.get_conditions()`: 无输入，返回 `(long_expr, short_expr)` 元组。
-   `CombinationEngine.register_family(family)`: 注册一个 `StrategyFamily`。
-   `CombinationEngine.generate_combinations()`: 返回一个迭代器，每次产出一个策略组合 `List[BaseStrategy]`。
-   `BacktestEngine.run(df, combination)`: 输入数据和策略组合，返回包含条件、统计和结果的字典。
-   `ResultHandler.log(result_dict)`: 接收`run`方法的输出并记录。

### 四、 工作流程说明 (Orchestrator)

下面是整个框架如何协同工作的流程，以一个主执行脚本的形式呈现。

```python
# main.py - 伪代码示例

import polars as pl
from pathlib import Path

# 1. 导入所有模块
from parameters import BollingerBandsParams, BandwidthStrategyParams, MidBandCrossStrategyParams # 假设存在
from indicators.bollinger import BollingerBandsIndicator
from strategies.bollinger import BandwidthNarrowingStrategy, MidBandCrossStrategy
from engine.combination_engine import CombinationEngine, StrategyFamily
from engine.backtest_engine import BacktestEngine
from engine.result_handler import ResultHandler # 假设存在一个日志处理器

# ==============================================================================
# 步骤 1: 初始化和注册 (Setup Phase)
# ==============================================================================
def setup_engine():
    engine = CombinationEngine()

    # --- 定义布林带策略族 ---
    bb_indicator = BollingerBandsIndicator(BollingerBandsParams(window=20, num_std=2))
    bb_strategies = [
        BandwidthNarrowingStrategy(BandwidthStrategyParams(lookback=3)),
        MidBandCrossStrategy(MidBandCrossStrategyParams()) # 假设有这个策略和参数类
    ]
    bollinger_family = StrategyFamily("Bollinger", bb_indicator, bb_strategies)
    engine.register_family(bollinger_family)

    # --- 定义其他策略族, e.g., MACD ---
    # macd_indicator = MACDIndicator(...)
    # macd_strategies = [MACDGoldenCrossStrategy(...), ...]
    # macd_family = StrategyFamily("MACD", macd_indicator, macd_strategies)
    # engine.register_family(macd_family)
    
    return engine

# ==============================================================================
# 步骤 2: 执行 (Execution Phase)
# ==============================================================================
def run_framework():
    # --- 全局配置 ---
    SYMBOLS = ["ETH-USDT", "BTC-USDT"]
    TIME_PERIODS = [
        {"start": "2023-01-01", "end": "2023-03-31"},
        {"start": "2023-04-01", "end": "2023-06-30"}
    ]
    DATA_PATH = Path("./data/")

    # --- 初始化所有组件 ---
    combo_engine = setup_engine()
    backtest_engine = BacktestEngine(returns_intervals=[10, 30, 60])
    result_handler = ResultHandler(log_file="backtest_results.log") # 初始化日志

    # --- 获取所有需要的指标和策略组合 ---
    all_indicators = combo_engine.get_all_indicators()
    all_combinations = list(combo_engine.generate_combinations()) # 物化组合列表

    print(f"Generated {len(all_combinations)} strategy combinations.")
    
    # --- 主循环 ---
    for period in TIME_PERIODS:
        for symbol in SYMBOLS:
            print(f"--- Running backtest for {symbol} from {period['start']} to {period['end']} ---")
            
            # 1. 加载数据
            try:
                # 假设有一个函数可以加载和筛选时间段的数据
                df = pl.read_parquet(DATA_PATH / f"{symbol}.parquet.0")
                df = df.filter(pl.col("start_time").is_between(period['start'], period['end']))
            except FileNotFoundError:
                print(f"Data for {symbol} not found. Skipping.")
                continue

            # 2. 计算所有需要的指标 (一次性完成)
            df_enriched = df
            for indicator in all_indicators:
                df_enriched = indicator.calculate(df_enriched)
            
            # 3. 遍历每一个策略组合
            for i, combination in enumerate(all_combinations):
                print(f"  -> Running combination {i+1}/{len(all_combinations)}")
                
                # 4. 执行回测
                results = backtest_engine.run(df_enriched, combination)

                # 5. 记录结果
                # 收集策略和参数信息
                strategy_info = {
                    "symbol": symbol,
                    "period": period,
                    "strategies": [strat.name for strat in combination],
                    "parameters": {strat.name: str(strat.params) for strat in combination}
                }
                
                # 合并所有信息并记录
                full_log_entry = {**strategy_info, **results}
                result_handler.log(full_log_entry)

if __name__ == "__main__":
    run_framework()
```

### 五、 对现有代码的分析和改进

1.  **耦合问题**: 你的 `BollingerSignal` 类混合了指标计算 (`calculate_bollinger_bands`) 和多个策略逻辑 (`bollinger_crossing_mid` 等)，这违反了单一职责原则。新设计将其拆分为 `BollingerBandsIndicator` 和多个 `BaseStrategy` 的子类。
2.  **硬编码**: `tolerance = 30` 这样的值被硬编码在方法内部。新设计通过 `ParameterSet` 类将其外部化，便于配置和优化。
3.  **返回类型**: 原有策略返回一个字典，包含 `start_time` 的列表。这导致了两个问题：
    *   **提前计算**: `.to_list()` 强制 Polars 立即执行计算并把数据拉到 Python 内存中，效率低下。
    *   **难以组合**: 两个 `start_time` 列表很难进行逻辑 "与" 操作。必须转换回 DataFrame 再进行 join/merge，非常繁琐。
    *   **改进**: 新设计返回 `polars.Expression`，这是一种符号化的、可组合的表示。所有组合逻辑都在 Polars 内部通过高效的 `&` 操作完成，直到最后一步才执行 `filter`。
4.  **参数传递**: 原有框架中，`window`, `num_std`, `lookback` 等参数在多个函数间传来传去。新设计中，每个类 (`Indicator` 或 `Strategy`) 在初始化时就接收自己的 `ParameterSet`，内部方法直接使用 `self.params`，更加清晰和内聚。

### 六、 输出格式和拓展

`ResultHandler` 类可以被设计为支持多种输出格式。

```python
# engine/result_handler.py

import json
import logging

class ResultHandler:
    def __init__(self, log_file: str):
        # 设置一个简单的日志记录器
        self.logger = logging.getLogger("BacktestLogger")
        self.logger.setLevel(logging.INFO)
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter('%(asctime)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def log(self, result_dict: dict):
        """记录一次回测的完整结果"""
        
        # 为了可读性，我们可以格式化输出
        # Polars DataFrame 对象不能直接JSON序列化，需要处理
        log_message = {k: v for k, v in result_dict.items() if not isinstance(v, pl.DataFrame)}
        
        log_message["long_results_summary"] = str(result_dict["long_results"]) # DataFrame转为字符串
        log_message["short_results_summary"] = str(result_dict["short_results"])
        
        # 使用json.dumps来创建一个格式化的字符串
        self.logger.info(json.dumps(log_message, indent=2))

        # 未来可以拓展到写入数据库、CSV等
    
    def write_to_db(self, result_dict):
        # ... 数据库写入逻辑 ...
        pass
```

**示例日志输出 (JSON格式):**
```json
{
  "symbol": "ETH-USDT",
  "period": {
    "start": "2023-01-01",
    "end": "2023-03-31"
  },
  "strategies": [
    "BandwidthNarrowingStrategy",
    "MACDGoldenCrossStrategy"
  ],
  "parameters": {
    "BandwidthNarrowingStrategy": "lookback=3",
    "MACDGoldenCrossStrategy": "fast=12, slow=26, signal=9"
  },
  "long_condition_str": "[[(col(\"bb_bandwidth\").shift([dyn int: 1])) < (col(\"bb_bandwidth\").shift([dyn int: 0]))].all_horizontal([[(col(\"bb_bandwidth\").shift([dyn int: 2])) < (col(\"bb_bandwidth\").shift([dyn int: 1]))], [(col(\"bb_bandwidth\").shift([dyn int: 3])) < (col(\"bb_bandwidth\").shift([dyn int: 2]))]]) & ((col(\"high\")) > (col(\"bb_upper\")))] & [(col(\"macd\").shift([dyn int: 1])) < (col(\"macd_signal\").shift([dyn int: 1]))].all_horizontal([[(col(\"macd\")) > (col(\"macd_signal\"))]])",
  "short_condition_str": "...",
  "long_entries_count": 152,
  "short_entries_count": 138,
  "long_results_summary": "shape: (1, 6)\n...",
  "short_results_summary": "shape: (1, 6)\n..."
}
```

这个设计方案提供了一个健壮、可扩展且易于维护的框架，能够满足你提出的所有需求，并为未来的功能拓展（如更复杂的风险管理、头寸管理、多参数优化等）打下了坚实的基础。

当然，非常乐意。将系统从代码细节提升到架构层面进行描述，是确保设计健壮性和可理解性的关键步骤。

下面我将为你详细阐述这个投资策略生成框架的整体设计，包括核心组件职责、类之间的静态关系（ER图）以及它们之间动态的调用逻辑（工作流/序列图）。

---

### 一、 核心组件 (Core Components) & 职责

我们将系统解构成一系列各司其职的组件，每个组件都是一个或多个类的集合，共同完成一项特定任务。

1.  **参数集 (ParameterSet)**
    *   **职责**: 作为纯粹的数据容器，存储一个指标或一个策略所需的所有可调参数（如 `window`, `lookback`）。
    *   **目的**: 将参数配置与算法逻辑完全解耦。使得参数可以被轻松地序列化、存储和用于自动化调优。

2.  **指标计算器 (Indicator)**
    *   **职责**: 接收一个 `polars.DataFrame` 和一个 `ParameterSet`，计算技术指标，并将结果作为新列添加回 `DataFrame`。
    *   **目的**: 封装纯粹的数学计算。它不关心任何交易逻辑，只负责生产“原材料”（即指标列）。这是一个可被多个策略复用的计算单元。

3.  **策略原语 (Strategy Primitive)**
    *   **职责**: 接收一个 `ParameterSet`，定义一组具体的做多/做空入场条件。它的核心产出是 `polars.Expression`，这是一个“延迟计算”的条件表达式，而不是最终的交易信号点。
    *   **目的**: 这是构成复杂策略的最小、最独立的逻辑单元。它的输出是可组合的，这是整个框架灵活性的基石。

4.  **策略族 (Strategy Family)**
    *   **职责**: 作为一个逻辑分组，它将**一个**指标计算器 (`Indicator`) 和**多个**基于该指标的策略原语 (`Strategy`) 关联起来。例如，“布林带”策略族包含一个布林带指标计算器和多个策略（如带宽策略、中轨穿越策略）。
    *   **目的**: 体现业务逻辑上的关联性，方便管理和注册。它清晰地表达了“基于这个指标，我们有这几种玩法”的概念。

5.  **组合引擎 (Combination Engine)**
    *   **职责**: 负责管理所有已注册的 `StrategyFamily`。其核心功能是计算所有策略族的笛卡尔积，生成一系列**策略组合**。每个组合都包含来自不同策略族的一个策略原语。
    *   **目的**: 自动化地、无遗漏地生成所有需要被回测的策略组合。它对具体的策略内容一无所知，只负责排列组合。

6.  **回测引擎 (Backtest Engine)**
    *   **职责**: 接收一个已经计算好所有指标列的 `DataFrame` 和一个**策略组合**。它将组合中所有策略原语的 `polars.Expression` 进行逻辑“与”(&)运算，形成最终的入场条件，然后执行过滤、计算回报和性能指标。
    *   **目的**: 这是一个纯粹的执行和评估单元。它将策略逻辑（`Expression`）应用于数据，并量化其表现。

7.  **结果处理器 (Result Handler)**
    *   **职责**: 接收来自回测引擎的完整结果（包括所用策略、参数、条件表达式、回测指标等），并将其以指定的格式（如Log、JSON、CSV、数据库）持久化存储。
    *   **目的**: 解耦回测执行与结果的存储。使输出格式的切换和扩展变得容易，而不影响回测核心逻辑。

8.  **执行器/编排器 (Orchestrator)**
    *   **职责**: 这是顶层控制器，是整个流程的“指挥官”。它负责初始化所有组件，定义回测范围（交易对、时间周期），并按照正确的顺序调用其他组件来完成整个回测任务。
    *   **目的**: 串联所有独立组件，管理整个系统的生命周期和工作流。

---

### 二、 类关系图 (Conceptual Class Relationship)

我们可以用文本形式来表示类之间的静态关系（继承、组合/聚合）。

```
[Orchestrator] (主执行脚本)
 |
 +--- (1) owns ---> [CombinationEngine]
 |      |
 |      +--- (N) holds ---> [StrategyFamily]
 |             |
 |             +--- (1) owns ---> [BaseIndicator] <---is-a--- [BollingerBandsIndicator]
 |             |      |
 |             |      +--- (1) owns ---> [BaseParameterSet] <---is-a--- [BollingerBandsParams]
 |             |
 |             +--- (N) owns ---> [BaseStrategy] <---is-a--- [BandwidthNarrowingStrategy]
 |                    |
 |                    +--- (1) owns ---> [BaseParameterSet] <---is-a--- [BandwidthStrategyParams]
 |
 +--- (1) owns ---> [BacktestEngine]
 |
 +--- (1) owns ---> [ResultHandler]
```

**关系解读:**

*   **`owns` (组合关系)**: 表示一个类的实例包含另一个类的实例。例如，`Orchestrator` 对象拥有一个 `CombinationEngine` 对象的实例。
*   **`holds` (聚合关系)**: 类似 `owns`，但生命周期不一定绑定。`CombinationEngine` 管理（持有）一系列 `StrategyFamily`。
*   **`is-a` (继承关系)**: 表示一个类是另一个类的子类。例如，`BollingerBandsIndicator` 是一个 `BaseIndicator`。
*   **(1) / (N)**: 表示一对一或一对多的关系。
    *   一个 `StrategyFamily` 包含 **一个** `Indicator` 和 **多个** (`N`) `Strategy`。
    *   一个 `CombinationEngine` 管理 **多个** (`N`) `StrategyFamily`。

---

### 三、 系统工作流 (System Workflow / Sequence)

这部分描述了当系统运行时，各个组件之间是如何按顺序交互的，就像一个UML序列图。

**阶段一：初始化与注册 (Setup Phase)**

1.  **`Orchestrator` 启动**：创建 `CombinationEngine`, `BacktestEngine`, `ResultHandler` 的实例。
2.  **`Orchestrator` 定义策略族**:
    a. 创建 `BollingerBandsParams` 和 `BandwidthStrategyParams` 等参数实例。
    b. 使用参数实例创建 `BollingerBandsIndicator` 和 `BandwidthNarrowingStrategy` 等指标和策略实例。
    c. 将上述指标和策略实例打包成一个 `StrategyFamily` 对象（例如 `bollinger_family`）。
3.  **`Orchestrator` 注册策略族**: 调用 `CombinationEngine.register_family(bollinger_family)`。
4.  **`Orchestrator` 重复步骤 2-3**: 为所有其他指标（如MACD, RSI）定义并注册它们的 `StrategyFamily`。

**阶段二：组合生成与准备 (Generation Phase)**

5.  **`Orchestrator` 请求组合**: 调用 `CombinationEngine.generate_combinations()`。
    *   `CombinationEngine` 内部对所有已注册的 `StrategyFamily` 的策略列表执行 `itertools.product`，生成一个包含所有策略组合的迭代器/列表。
6.  **`Orchestrator` 请求所需指标**: 调用 `CombinationEngine.get_all_indicators()`，获取一个去重后的、所有组合会用到的指标对象列表。

**阶段三：回测执行循环 (Execution Loop Phase)**

7.  **`Orchestrator` 开始外层循环**: `for period in TIME_PERIODS:`
8.  **`Orchestrator` 开始内层循环**: `for symbol in SYMBOLS:`
    a. **加载数据**: `Orchestrator` 读取对应 `symbol` 和 `period` 的原始 `DataFrame`。
    b. **计算指标 (一次性)**: `Orchestrator` 遍历**阶段二**获取的 `all_indicators` 列表，对当前 `DataFrame` 依次调用 `indicator.calculate(df)`，生成一个包含所有必需指标列的 `df_enriched`。这一步保证了每个指标只被计算一次，效率很高。
    c. **`Orchestrator` 开始遍历策略组合**: `for combination in all_combinations:`
        i. **执行回测**: `Orchestrator` 调用 `BacktestEngine.run(df_enriched, combination)`。
            *   **`BacktestEngine` 内部**:
                1.  从 `combination` 列表中获取每个 `strategy` 实例。
                2.  调用每个 `strategy.get_conditions()`，收集所有的 `long_expr` 和 `short_expr`。
                3.  使用 `pl.all_horizontal()` 将所有 `long_expr` 聚合为 `final_long_cond`，对 `short_expr` 做同样操作。
                4.  使用 `df_enriched.filter(final_long_cond)` 筛选出多头入场点。
                5.  计算回报率和各项统计指标。
                6.  将所有结果（条件表达式字符串、入场点数量、统计指标等）打包成一个 `result_dict`。
                7.  返回 `result_dict` 给 `Orchestrator`。
        ii. **记录结果**: `Orchestrator` 接收到 `result_dict`。
            1.  `Orchestrator` 为 `result_dict` 添加额外的元数据，如 `symbol`, `period`, `strategy_names`, `parameters`。
            2.  `Orchestrator` 调用 `ResultHandler.log(full_result_dict)`。
                *   **`ResultHandler` 内部**: 将接收到的字典格式化为指定的输出格式（如JSON），并写入日志文件或数据库。
    d. 内层循环继续，直到所有组合在该 `symbol` 和 `period` 上都测试完毕。
9.  外层循环继续，直到所有 `symbol` 和 `period` 都被遍历。

**流程结束。**

这个三段式的描述（组件职责、静态关系、动态流程）为你提供了一个从宏观到微观的完整系统视图，清晰地展示了框架的设计思想和运作机制。