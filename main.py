import os
import polars as pl
import concurrent.futures
import time
from concurrent.futures import ThreadPoolExecutor
import argparse
from queue import Queue
import threading

from coin_list_fetcher import fetch_coin_list, filter_by_symbol_list, save_to_parquet
from coin_history_fetcher import fetch_coin_history, save_to_parquet as save_history_to_parquet

class CoinDataCollector:
    def __init__(self, 
                 workers=5, 
                 coins_cache_path="collected_coins.parquet",
                 symbol_list_path="symbol_list.csv",
                 interval_output_path="collected_coins_interval.parquet"):
        self.workers = workers
        self.coins_cache_path = coins_cache_path
        self.symbol_list_path = symbol_list_path
        self.interval_output_path = interval_output_path
        
        # 使用阻塞队列替代锁
        self.interval_queue = Queue()
        self.interval_data = []
        
        # 启动一个专门的线程处理间隔数据
        self.interval_processor = threading.Thread(target=self._process_interval_data)
        self.interval_processor.daemon = True
        self.interval_processor.start()

    def _process_interval_data(self):
        """处理间隔数据的专用线程"""
        while True:
            # 从队列中获取数据，这会阻塞直到队列中有数据
            item = self.interval_queue.get()
            
            # 特殊标记，表示处理完成
            if item is None:
                self.interval_queue.task_done()
                break
            
            self.interval_data.append(item)
            self.interval_queue.task_done()

    def get_coin_list(self):
        """获取符合条件的币种列表"""
        # 如果本地已缓存，直接读取
        if os.path.exists(self.coins_cache_path):
            print(f"使用本地缓存的币种列表: {self.coins_cache_path}")
            return pl.read_parquet(self.coins_cache_path)
        
        # 否则，获取所有币种并过滤
        df = fetch_coin_list()
        if df is not None and not df.is_empty():
            filtered_df = filter_by_symbol_list(df, self.symbol_list_path)
            save_to_parquet(filtered_df, self.coins_cache_path)
            return filtered_df
        
        return None

    def process_coin(self, coin_id, symbol):
        """处理单个币种的历史数据"""
        print(f"正在获取 {symbol}({coin_id}) 的历史数据...")
        
        df, symbol, first_timestamp, last_timestamp, current_market_cap = fetch_coin_history(coin_id)
        
        if df is not None and not df.is_empty():
            # 保存历史数据
            output_path = save_history_to_parquet(df, symbol, coin_id, first_timestamp, last_timestamp)
            
            # 使用队列安全地更新间隔数据，无需显式锁
            self.interval_queue.put({
                "symbol": symbol,
                "id": coin_id,
                "first_timestamp": first_timestamp,
                "last_timestamp": last_timestamp,
                "current_market_cap": current_market_cap
            })
            
            return True, symbol, coin_id
        
        return False, symbol, coin_id

    def save_interval_data(self):
        """保存币种时间间隔数据"""
        # 发送结束信号给处理线程
        self.interval_queue.put(None)
        # 等待所有任务完成
        self.interval_queue.join()
        self.interval_processor.join()
        
        if not self.interval_data:
            print("没有间隔数据可保存")
            return
        
        df = pl.DataFrame(self.interval_data)
        df.write_parquet(self.interval_output_path)
        print(f"币种时间间隔数据已保存到 {self.interval_output_path}")
        print(f"收集了 {len(self.interval_data)} 个币种的时间间隔数据")

    def run(self):
        """运行主程序"""
        # 获取币种列表
        coins_df = self.get_coin_list()
        
        if coins_df is None or coins_df.is_empty():
            print("未获取到符合条件的币种列表")
            return
        
        print(f"获取到 {len(coins_df)} 个符合条件的币种")
        
        # 提取币种ID和符号
        coins_data = coins_df.select(["id", "symbol"]).to_dicts()
        
        # 使用线程池处理每个币种
        successful = 0
        failed = 0
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.workers) as executor:
            futures = [executor.submit(self.process_coin, coin["id"], coin["symbol"]) for coin in coins_data]
            
            for future in concurrent.futures.as_completed(futures):
                try:
                    result, symbol, coin_id = future.result()
                    if result:
                        successful += 1
                        print(f"成功处理 {symbol}({coin_id})")
                    else:
                        failed += 1
                        print(f"处理 {symbol}({coin_id}) 失败")
                except Exception as e:
                    failed += 1
                    print(f"处理币种时出错: {e}")
        
        end_time = time.time()
        
        # 保存时间间隔数据
        self.save_interval_data()
        
        print(f"任务完成. 总计: {len(coins_data)}, 成功: {successful}, 失败: {failed}")
        print(f"耗时: {end_time - start_time:.2f} 秒")

def main():
    parser = argparse.ArgumentParser(description="加密货币数据收集器")
    parser.add_argument("--workers", type=int, default=5, help="线程池工作线程数")
    parser.add_argument("--coins_cache", type=str, default="collected_coins.parquet", help="币种缓存文件路径")
    parser.add_argument("--symbol_list", type=str, default="symbol_list.csv", help="符号列表文件路径")
    parser.add_argument("--interval_output", type=str, default="collected_coins_interval.parquet", 
                        help="间隔数据输出文件路径")
    
    args = parser.parse_args()
    
    collector = CoinDataCollector(
        workers=args.workers, 
        coins_cache_path=args.coins_cache,
        symbol_list_path=args.symbol_list,
        interval_output_path=args.interval_output
    )
    
    collector.run()

if __name__ == "__main__":
    main() 