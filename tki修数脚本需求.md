## 1. 已有数据

我现在在/data/token_insight/coin_mkc/day目录下有一系列的数据文件，文件格式为：BTC_bitcoin_history_mkc.parquet，同时，这个目录下还有collect_status.parquet，collected_coins_interval.parquet，inc_collect_status.parquet三个标识性质的parquet文件，其中collect_status.parquet的格式是：
```
shape: (297, 3)
┌─────────────────────────┬────────┬────────┐
│ id                      ┆ symbol ┆ status │
│ ---                     ┆ ---    ┆ ---    │
│ str                     ┆ str    ┆ bool   │
╞═════════════════════════╪════════╪════════╡
│ navi                    ┆ NAVX   ┆ true   │
│ analysoor               ┆ ZERO   ┆ true   │
```

此外，我在"/data/token_insight/meta/symbol_list.parquet"路径有一个symbol_list.parquet，它的格式是这样：
```shape: (593, 1)
┌─────────┐
│ symbol  │
│ ---     │
│ str     │
╞═════════╡
│ STRAT   │
│ DENT    │
│ GBP     │
...
```
而在"/data/token_insight/coin_list/collected_coins.parquet"，我有一个token insight数据源支持的全量币种数据文件：collected_coins.parquet，它的格式是这样：
```
shape: (15_704, 5)
┌───────────────┬─────────┬────────────────────────────┬─────────────────┬─────────────────────────────┐
│ price         ┆ symbol  ┆ id                         ┆ spot_volume_24h ┆ price_change_percentage_24h │
│ ---           ┆ ---     ┆ ---                        ┆ ---             ┆ ---                         │
│ f64           ┆ str     ┆ str                        ┆ f64             ┆ f64                         │
╞═══════════════╪═════════╪════════════════════════════╪═════════════════╪═════════════════════════════╡
│ 105655.642473 ┆ BTC     ┆ bitcoin                    ┆ 7.9614e9        ┆ -0.000015                   │
│ 2496.943547   ┆ ETH     ┆ ethereum                   ┆ 4.0022e9        ┆ -0.008493                   │
│ 1.000424      ┆ USDT    ┆ tether                     ┆ 1.5428e8        ┆ 0.000011                    │
```

## 2.任务目标
现在我需要你：

用collect_status.parquet的symbol列与"/data/token_insight/meta/symbol_list.parquet"的symbol列做对比，
1. 如果一个symbol仅出现在collect_status.parquet中，则把它对应的一行从collect_status.parquet中删除掉

2. 如果一个symbol仅出现在symbol_list.parquet中，则在collected_status.parquet中加入这个symbol，按照id，symbol，status的格式，其中id通过根据symbol反查collected_coins.parquet的id列获得，status为False（假设TRUMP只在symbol_list.parquet中出现，没在collected_status.parquet中出现，则将["official-trump", "TRUMP", False]加入到colleted_status.parquet中, 其中official-trump来自collected_coins.parquet，通过TRUMP查到）

3. 如果一个symbol在collect_status.parquet和ymbol_list.parquet中都出现了，则保留collected_status.parquet中这个symbol的行，不做改变。

完成之后更新collected_status.parquet

## 3.编码要求
使用polars库对文件进行操作