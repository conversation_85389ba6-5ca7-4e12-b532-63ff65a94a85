"""
IoC容器 - 依赖注入容器

实现Spring风格的IoC容器，管理Signal和Indicator的生命周期
"""

from code.core.log import LK
from .godtrader.core.base import BaseSignal, BaseIndicator

import hashlib
from typing import Dict, Any, Optional, Type, List, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod




@dataclass
class BeanDefinition:
    """Bean定义"""
    name: str
    bean_type: str  # 'indicator' or 'signal'
    class_name: str
    config: Dict[str, Any]
    singleton: bool = True
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


@dataclass
class BeanInstance:
    """Bean实例"""
    definition: BeanDefinition
    instance: Any
    cache_key: str
    created_at: float


class DependencyResolver:
    """依赖解析器"""
    
    def __init__(self, container: 'BeanManager'):
        self.container = container
        self.logger = LK
        self._resolving_stack: List[str] = []
    
    def resolve_dependencies(self, bean_name: str) -> List[Any]:
        """解析Bean的依赖"""
        if bean_name in self._resolving_stack:
            cycle = " -> ".join(self._resolving_stack + [bean_name])
            raise ValueError(f"检测到循环依赖: {cycle}")
        
        definition = self.container.get_bean_definition(bean_name)
        dependencies = []
        
        self._resolving_stack.append(bean_name)
        try:
            for dep_name in definition.dependencies:
                dep_instance = self.container.get_bean(dep_name)
                dependencies.append(dep_instance)
        finally:
            self._resolving_stack.pop()
        
        return dependencies


# global registries of indicator and signal
_GLOBAL_INDICATOR_CLASSES: Dict[str, Type[BaseIndicator]] = {}
_GLOBAL_SIGNAL_CLASSES: Dict[str, Type[BaseSignal]] = {}

def register_indicator(name:str):
    def decorator(cls: Type[BaseIndicator]):
        _GLOBAL_INDICATOR_CLASSES[name] = cls
        return cls
    return decorator

def register_signal(name:str):
    def decorator(cls: Type[BaseSignal]):
        _GLOBAL_SIGNAL_CLASSES[name] = cls
        return cls
    return decorator

class BeanManager:
    """信号容器 - 轻量级IoC实现
    
    功能：
    1. 配置驱动的Bean定义注册
    2. 单例和原型模式支持
    3. 依赖注入和循环依赖检测
    4. 参数化实例缓存
    5. 生命周期管理
    """
    
    def __init__(self):
        self.logger = LK

        self._indicator_classes: Dict[str, Type[BaseIndicator]] = {}
        self._signal_classes: Dict[str, Type[BaseSignal]] = {}
        
        # Bean定义存储
        self._bean_definitions: Dict[str, BeanDefinition] = {}
        
        # 实例缓存
        self._singleton_instances: Dict[str, Any] = {}
        self._prototype_instances: Dict[str, BeanInstance] = {}
        
        # 依赖解析器
        self._dependency_resolver = DependencyResolver(self)
        
        # 配置
        self._config: Optional[Dict[str, Any]] = None
    
    def _register_indicator_class(self, name: str, indicator_class: Type[BaseIndicator]):
        self._indicator_classes[name] = indicator_class
    
    def _register_signal_class(self, name: str, signal_class: Type[BaseSignal]):
        self._signal_classes[name] = signal_class
    
    def get_indicator_class(self, name: str) -> Type[BaseIndicator]:
        if name not in self._indicator_classes:
            raise ValueError(f"Unregistered indicator: {name}")
        return self._indicator_classes[name]
    
    def get_signal_class(self, name: str) -> Type[BaseSignal]:
        if name not in self._signal_classes:
            raise ValueError(f"Unregistered signal: {name}")
        return self._signal_classes[name]

    def _load_global_registries(self):
        self._indicator_classes.update(_GLOBAL_INDICATOR_CLASSES)
        self._signal_classes.update(_GLOBAL_SIGNAL_CLASSES)

    def load_config(self, config: Dict[str, Any]):
        """加载配置并注册Bean定义"""
        self._config = config
        self.logger.info("开始加载IoC配置")
        
        # 注册Indicator定义
        self._register_indicator_definitions(config.get('indicators', {}))
        
        # 注册Signal定义
        self._register_signal_definitions(config.get('signals', {}))
        
        self.logger.info(f"IoC容器加载完成，共注册 {len(self._bean_definitions)} 个Bean定义")
    
    def _register_indicator_definitions(self, indicators_config: Dict[str, Any]):
        """注册Indicator定义"""
        for name, indicator_config in indicators_config.items():
            # 从配置中提取类名
            class_name = indicator_config.get('class', name)
            
            definition = BeanDefinition(
                name=name,
                bean_type='indicator',
                class_name=class_name,
                config=indicator_config,
                singleton=False,  # Indicator支持参数化，使用原型模式
                dependencies=[]
            )
            
            self._bean_definitions[name] = definition
            self.logger.debug(f"注册Indicator定义: {name}")
    
    def _register_signal_definitions(self, signals_config: Dict[str, Any]):
        """注册Signal定义"""
        for name, signal_config in signals_config.items():
            # 从配置中提取类名和依赖
            class_name = signal_config.get('class', name)
            indicator_name = signal_config.get('indicator')
            
            dependencies = []
            if indicator_name:
                dependencies.append(indicator_name)
            
            definition = BeanDefinition(
                name=name,
                bean_type='signal',
                class_name=class_name,
                config=signal_config,
                singleton=True,  # Signal通常是单例
                dependencies=dependencies
            )
            
            self._bean_definitions[name] = definition
            self.logger.debug(f"注册Signal定义: {name} (依赖: {dependencies})")
    
    def get_bean_definition(self, name: str) -> BeanDefinition:
        """获取Bean定义"""
        if name not in self._bean_definitions:
            raise ValueError(f"未找到Bean定义: {name}")
        return self._bean_definitions[name]
    
    def get_bean(self, name: str) -> Any:
        """获取Bean实例
        
        Args:
            name: Bean名称
            params: 参数（用于原型模式）
            
        Returns:
            Bean实例
        """
        definition = self.get_bean_definition(name)
        
        if definition.singleton:
            return self._get_singleton_instance(definition)
        else:
            return self._get_prototype_instance(definition)
    
    def _get_singleton_instance(self, definition: BeanDefinition) -> Any:
        """获取单例实例"""
        if definition.name in self._singleton_instances:
            return self._singleton_instances[definition.name]
        
        # 创建实例
        instance = self._create_instance(definition)
        self._singleton_instances[definition.name] = instance
        
        return instance
    
    def _get_prototype_instance(self, definition: BeanDefinition) -> Any:
        """获取原型实例（支持参数化缓存）"""
        cache_key = self._generate_cache_key(definition.name) #TODO:改
        
        if cache_key in self._prototype_instances:
            return self._prototype_instances[cache_key].instance
        
        # 创建实例
        instance = self._create_instance(definition)
        
        # 缓存实例
        bean_instance = BeanInstance(
            definition=definition,
            instance=instance,
            cache_key=cache_key,
            created_at=0  # 可以添加时间戳
        )
        self._prototype_instances[cache_key] = bean_instance
        
        return instance
    
    def _create_instance(self, definition: BeanDefinition) -> Any:
        """创建Bean实例"""
        try:
            # 解析依赖
            dependencies = self._dependency_resolver.resolve_dependencies(definition.name)
            
            if definition.bean_type == 'indicator':
                return self._create_indicator_instance(definition, dependencies)
            elif definition.bean_type == 'signal':
                return self._create_signal_instance(definition, dependencies)
            else:
                raise ValueError(f"不支持的Bean类型: {definition.bean_type}")
                
        except Exception as e:
            self.logger.error(f"创建Bean实例失败 {definition.name}: {e}")
            raise
    
    def _create_indicator_instance(self, definition: BeanDefinition, dependencies: List[Any]) -> BaseIndicator:
        """创建Indicator实例"""
        indicator_class: Type[BaseIndicator] = self._indicator_classes.get(definition.class_name)
        instance = indicator_class().from_config(definition.config)
        return instance
    
    def _create_signal_instance(self, definition: BeanDefinition, dependencies: List[Any]) -> BaseSignal:
        """创建Signal实例"""

        # 从注册表获取类
        signal_class: Type[BaseSignal] = self._signal_classes.get(definition.class_name)

        #TODO:load paramspace from definition.config

        # 准备构造参数
        kwargs = {}

        # 检查构造函数签名来决定如何传递参数
        import inspect
        sig = inspect.signature(signal_class.__init__)
        params = list(sig.parameters.keys())

        # 添加信号名称（如果构造函数接受name参数）
        if 'name' in params:
            kwargs['name'] = definition.name

        # 注入Indicator依赖（如果构造函数接受indicator_name参数）
        if 'indicator' in params and definition.dependencies:
            kwargs['indicator'] = definition.dependencies[0]

        # 创建实例
        instance = signal_class(**kwargs)

        return instance
    
    def _generate_cache_key(self, name: str, params: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 将参数序列化为字符串
        params_str = str(sorted(params.items()))
        cache_str = f"{name}_{params_str}"
        
        # 生成哈希
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def get_bean_names(self, bean_type: Optional[str] = None) -> List[str]:
        """获取Bean名称列表"""
        if bean_type is None:
            return list(self._bean_definitions.keys())
        
        return [name for name, definition in self._bean_definitions.items() 
                if definition.bean_type == bean_type]
    
    def clear_cache(self):
        """清空缓存"""
        self._singleton_instances.clear()
        self._prototype_instances.clear()
        self.logger.info("IoC容器缓存已清空")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取容器统计信息"""
        return {
            "bean_definitions": len(self._bean_definitions),
            "singleton_instances": len(self._singleton_instances),
            "prototype_instances": len(self._prototype_instances),
            "indicator_definitions": len([d for d in self._bean_definitions.values() if d.bean_type == 'indicator']),
            "signal_definitions": len([d for d in self._bean_definitions.values() if d.bean_type == 'signal'])
        }
