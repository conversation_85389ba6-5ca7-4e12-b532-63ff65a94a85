from code.core.config import CONFIG
from core.base import BaseIndicator, BaseSignal

from typing import Dict, Type


GLOBAL_INDICATOR_REGISTRY: Dict[str, tuple[Type[BaseIndicator], dict]] = {} # name -> (cls, parma_space)
GLOBAL_SIGNAL_REGISTRY: Dict[str, tuple[Type[BaseSignal], dict]] = {}
ICFG: Dict[str, dict] = CONFIG.get("indicators", {})
SCFG: Dict[str, dict] = CONFIG.get("signals", {})

def get_all_indicators():
    return GLOBAL_INDICATOR_REGISTRY.copy()


def get_all_signals():
    return GLOBAL_SIGNAL_REGISTRY.copy()

def register_indicator(name: str):
    def decorator(cls: Type[BaseIndicator]):
        GLOBAL_INDICATOR_REGISTRY[name] = (cls, ICFG.get(name, {}))
        return cls
    return decorator

def register_signal(name: str):
    def decorator(cls: Type[BaseSignal]):
        GLOBAL_SIGNAL_REGISTRY[name] = (cls, SCFG.get(name, {}))
        return cls
    return decorator
