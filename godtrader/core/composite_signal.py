from base import BaseSignal
from leaf_signal import LeafSignal
from params import ParamSpace
from registry import GLOBAL_SIGNAL_REGISTRY
from code.core.log import LK

import polars as pl
from typing import List, Dict, Any
from itertools import product


"""
param_space:
# 组合配置
compositions:
    params:
        max_children: 
            default: 3      # 每个组合最多包含的信号数
        operators:                    # 支持的组合操作符
            type: categorical
            range: ["and", "or", "vote", "weighted_sum"]

        [optional]
        weighted_sum:
        threshold_range: [0.3, 0.7]
        weight_step: 0.1
        majority:
        threshold: 0.5

        # [param_dict]
        # children: List[Dict[str, Any]]
"""

class CompositeSignal(BaseSignal):

    def __init__(self, children: List[BaseSignal], name = None, param_space = None, params = None):
        super().__init__(name, param_space, params)
        self.children = children 
        self.name = self._concat_children_name() #TODO: 改进为更合理的命名法，兼顾可读性和唯一性
        

    
    def _concat_children_name(self):
        return "_".join([child.name for child in self.children])

    def save_signal(self, path, mode = "log"):
        return super().save_signal(path, mode)

    def compute_features(self, df: pl.DataFrame) -> pl.DataFrame: # instance compute feature for children
        """computes features for all children and alias columns in the final df
        """
        for child in self.children:
            df = child.compute_features(df)
        
        return df
            

    def generate_condition(self, df: pl.DataFrame) -> pl.Expr: # instance generate condition for children
        conditions = [child.generate_condition(df) for child in self.children]
        return self._combine_conditions(conditions)


    def _combine_conditions(self, conditions: List[pl.Expr]) -> pl.Expr:
        if len(conditions) == 1:
            return conditions[0]
        
        operator = self.params["operator"]
        if operator == "and":
            return pl.all_horizontal(conditions)
        elif operator == "or":
            return pl.any_horizontal(conditions)
        elif operator == "vote":
            return pl.sum_horizontal(conditions) / len(conditions) >= self.params.get("vote_threshold", 0.5)
        # elif operator == "weighted_sum":
        #     weights = self.params["weights"]
        #     return pl.sum_horizontal(w * c for w, c in zip(weights, conditions))
        else:
            self.logger.error(f"Unknown operator: {self.params["operator"]}")
            raise ValueError(f"Unknown operator: {self.params['operator']}")

    def generate_param_combinations(self) -> List[Dict[str, Any]]: # the origin generates param combinations
        """generate param combinations for all children
        """

        """
        返回：
        [
          {                       # 一条完整组合（树形）
            "type": "composite",
            "operator": "and",
            "signal_name": self.name,
            "children": [
                { "type": "leaf", "signal_name": "leaf1", "indicator_params": {...}, "signal_params": {...} },
                { "type": "composite", "operator": "or", "children": [...] },
                ...
            ]
          },
          ...                     # 所有笛卡尔积
        ]
        """

        child_combo_lists: List[List[Dict[str, Any]]] = []
        for child in self.children:
            if isinstance(child, LeafSignal):
                child_combo_lists.append(self._leaf_combinations(child))
            else:
                child_combo_lists.append(child.generate_param_combinations())

        # 2. 子树笛卡尔积
        product_of_children: List[tuple] = list(product(*child_combo_lists))

        # 3. 本节点参数组合（新增）
        self_params = self.param_space.sample()          # 返回 List[Dict]
        operators: List[str] = self_params["operators"]  # 例如 ["and","or"]
        threshold = self_params["vote_threshold"]

        # 4. 再把「operator 取值」与「子树组合」做一次二维积
        final: List[Dict[str, Any]] = []
        for op in operators:
            for one_child_tuple in product_of_children:
                final.append({
                    "type": "composite",
                    "signal_name": self.name,
                    "operator": op,                   # ← 这里放进去
                    "vote_threshold": threshold,
                    "children": list(one_child_tuple)
                })
        return final
    
    @staticmethod
    def _leaf_combinations(leaf: LeafSignal) -> List[Dict[str, Any]]:
        indicator_params_list = leaf.indicator.param_space.sample()
        signal_params_list   = leaf.param_space.sample()
        out: List[Dict[str, Any]] = []
        for ind_param in indicator_params_list:
            for sig_param in signal_params_list:
                out.append({
                    "type": "leaf",
                    "signal_name": leaf.name,
                    "indicator_params": ind_param,
                    "signal_params": sig_param
                })
        return out
    

    @classmethod
    def from_config(cls, config: Dict[str, Any], **kwargs: Any):
        children: List[BaseSignal] = kwargs.get("children")
        param_space = ParamSpace.from_config(config)
        return cls(children=children, param_space=param_space)

    @classmethod
    def from_param_dict(cls, params: Dict[str, Any]):
        children = []
        for child_param in params["children"]:
            child = None
            if child_param["type"] == "leaf":
                cls, _ = GLOBAL_SIGNAL_REGISTRY.get(child_param["signal_name"])
                child = cls.from_param_dict(child_param)
            elif child_param["type"] == "composite":
                cls = CompositeSignal
                child = cls.from_param_dict(child_param)
            else:
                LK.error(f"Unknown signal type: {child_param['type']}")
                raise ValueError(f"Unknown signal type: {child_param['type']}")
            
            children.append(child)

        params.pop("children")
        return cls(children=children, params=params)