from base import BaseSignal, BaseIndicator
from registry import GLOBAL_INDICATOR_REGISTRY
from params import ParamSpace

import polars as pl
from typing import Dict, Any


class LeafSignal(BaseSignal):

    def __init__(self, name, param_space = None, params = None):
        super().__init__(name, param_space, params)
        self.indicator = None
        self._setup_param()

    def _setup_param(self):
        """setup param space for origins or setup params for instances
        """
        if self.params is not None: # instance for backtest
            indicator_name = self.params["indicator_name"]
            cls, _= GLOBAL_INDICATOR_REGISTRY.get(indicator_name)
            if cls is None:
                self.logger.error(f"Indicator {indicator_name} not found", f_lvl=2)
                raise ValueError(f"Indicator {indicator_name} not found")
            indicator_params = self.params.get("indicator_params")
            self.indicator = cls.from_param_dict(indicator_params)

        elif self.param_space is not None: # origin for generating params
            indicator_name = self.param_space.get_descriptor("indicator").name
            cls, indicator_param_space = GLOBAL_INDICATOR_REGISTRY.get(indicator_name, None)
            if cls is None:
                self.logger.error(f"Indicator {indicator_name} not found", f_lvl=2)
                raise ValueError(f"Indicator {indicator_name} not found")
            self.indicator = cls.from_config(indicator_param_space)
            
    
    def compute_features(self, df: pl.DataFrame):
        return self.indicator.compute(df)



    @classmethod
    def from_config(cls, config: Dict[str, Any], **kwargs: Any):
        param_space = ParamSpace.from_config(config)
        return cls(config.keys()[0], param_space=param_space)
    
    @classmethod
    def from_param_dict(cls, params: Dict[str, Any]):
        return cls(params["signal_name"], params=params)