#!/usr/bin/env python3
"""
unitest/params.py 模块单元测试

专门测试 unitest/params.py 中的参数管理功能
"""

import unittest
import sys
from pathlib import Path
import random

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入要测试的模块
from params import (
    ParamDescriptor, 
    ParamSpace, 
    GridSampling, 
    RandomSampling, 
    AssignSampling,
    ISamplingStrategy
)


class TestParamDescriptor(unittest.TestCase):
    """测试 ParamDescriptor 类"""
    
    def setUp(self):
        """测试前准备"""
        random.seed(42)
    
    def test_int_param_descriptor_creation(self):
        """测试整数参数描述符创建"""
        desc = ParamDescriptor(
            name="window",
            type_="int",
            low=10,
            high=30,
            step=5,
            default=20
        )
        
        self.assertEqual(desc.name, "window")
        self.assertEqual(desc.type_, "int")
        self.assertEqual(desc.low, 10)
        self.assertEqual(desc.high, 30)
        self.assertEqual(desc.step, 5)
        self.assertEqual(desc.default, 20)
    
    def test_float_param_descriptor_creation(self):
        """测试浮点数参数描述符创建"""
        desc = ParamDescriptor(
            name="threshold",
            type_="float",
            low=0.1,
            high=0.9,
            step=0.1,
            default=0.5
        )
        
        self.assertEqual(desc.name, "threshold")
        self.assertEqual(desc.type_, "float")
        self.assertAlmostEqual(desc.low, 0.1)
        self.assertAlmostEqual(desc.high, 0.9)
        self.assertAlmostEqual(desc.step, 0.1)
        self.assertAlmostEqual(desc.default, 0.5)
    
    def test_categorical_param_descriptor_creation(self):
        """测试分类参数描述符创建"""
        desc = ParamDescriptor(
            name="strategy",
            type_="categorical",
            choices=["fast", "slow", "medium"],
            default="medium"
        )
        
        self.assertEqual(desc.name, "strategy")
        self.assertEqual(desc.type_, "categorical")
        self.assertEqual(desc.choices, ["fast", "slow", "medium"])
        self.assertEqual(desc.default, "medium")
    
    def test_assigned_param_descriptor(self):
        """测试指定值参数描述符"""
        desc = ParamDescriptor(
            name="fixed_param",
            type_="int",
            default=42
        )
        
        # 指定值参数应该不需要 low/high
        self.assertEqual(desc.name, "fixed_param")
        self.assertEqual(desc.default, 42)
    
    def test_int_param_validation_missing_bounds(self):
        """测试整数参数缺少边界值的验证"""
        with self.assertRaises(ValueError) as context:
            ParamDescriptor(name="test", type_="int", low=10)
        
        self.assertIn("low and high value must be assigned", str(context.exception))
        
        with self.assertRaises(ValueError) as context:
            ParamDescriptor(name="test", type_="int", high=30)
        
        self.assertIn("low and high value must be assigned", str(context.exception))
    
    def test_int_param_validation_invalid_bounds(self):
        """测试整数参数无效边界值的验证"""
        with self.assertRaises(ValueError) as context:
            ParamDescriptor(name="test", type_="int", low=30, high=10)
        
        self.assertIn("got low > high", str(context.exception))
    
    def test_categorical_param_validation_missing_choices(self):
        """测试分类参数缺少选择项的验证"""
        with self.assertRaises(ValueError) as context:
            ParamDescriptor(name="test", type_="categorical")
        
        self.assertIn("choises must be assigned", str(context.exception))
        
        with self.assertRaises(ValueError) as context:
            ParamDescriptor(name="test", type_="categorical", choices=[])
        
        self.assertIn("choises must be assigned", str(context.exception))
    
    def test_int_grid_generation(self):
        """测试整数网格生成"""
        desc = ParamDescriptor(
            name="window",
            type_="int",
            low=10,
            high=20,
            step=5
        )
        
        values = desc.generate_values("grid")
        expected = [10, 15, 20]
        self.assertEqual(values, expected)
    
    def test_float_grid_generation(self):
        """测试浮点数网格生成"""
        desc = ParamDescriptor(
            name="threshold",
            type_="float",
            low=0.0,
            high=1.0,
            step=0.25
        )
        
        values = desc.generate_values("grid")
        expected = [0.0, 0.25, 0.5, 0.75, 1.0]
        
        self.assertEqual(len(values), len(expected))
        for actual, exp in zip(values, expected):
            self.assertAlmostEqual(actual, exp, places=6)
    
    def test_categorical_generation(self):
        """测试分类参数生成"""
        desc = ParamDescriptor(
            name="strategy",
            type_="categorical",
            choices=["fast", "slow", "medium"]
        )
        
        values = desc.generate_values("grid")
        self.assertEqual(values, ["fast", "slow", "medium"])
        
        values = desc.generate_values("random", 5)
        self.assertEqual(values, ["fast", "slow", "medium"])
    
    def test_int_random_generation(self):
        """测试整数随机生成"""
        desc = ParamDescriptor(
            name="window",
            type_="int",
            low=10,
            high=20
        )
        
        values = desc.generate_values("random", 5)
        
        self.assertEqual(len(values), 5)
        for value in values:
            self.assertIsInstance(value, int)
            self.assertGreaterEqual(value, 10)
            self.assertLessEqual(value, 20)
    
    def test_float_random_generation(self):
        """测试浮点数随机生成"""
        desc = ParamDescriptor(
            name="threshold",
            type_="float",
            low=0.1,
            high=0.9
        )
        
        values = desc.generate_values("random", 5)
        
        self.assertEqual(len(values), 5)
        for value in values:
            self.assertIsInstance(value, float)
            self.assertGreaterEqual(value, 0.1)
            self.assertLessEqual(value, 0.9)
    
    def test_assign_strategy(self):
        """测试指定值策略"""
        desc = ParamDescriptor(
            name="fixed",
            type_="int",
            default=42
        )
        
        # assign 策略应该返回 None（根据代码实现）
        result = desc.generate_values("assign")
        self.assertIsNone(result)
    
    def test_unsupported_strategy(self):
        """测试不支持的采样策略"""
        desc = ParamDescriptor(
            name="test",
            type_="int",
            low=1,
            high=10
        )
        
        with self.assertRaises(ValueError) as context:
            desc.generate_values("unsupported_strategy")
        
        self.assertIn("unsupported sampling strategy", str(context.exception))


class TestSamplingStrategies(unittest.TestCase):
    """测试采样策略类"""
    
    def setUp(self):
        """测试前准备"""
        random.seed(42)
        
        self.descriptors = [
            ParamDescriptor(name="x", type_="int", low=1, high=3, step=1),
            ParamDescriptor(name="y", type_="float", low=0.0, high=1.0, step=0.5)
        ]
        self.param_space = ParamSpace(self.descriptors)
    
    def test_grid_sampling_strategy(self):
        """测试网格采样策略"""
        strategy = GridSampling()
        combinations = strategy.sample(self.param_space)
        
        # 预期: 3 * 3 = 9 个组合
        self.assertEqual(len(combinations), 9)
        
        # 检查是否包含所有可能的组合
        x_values = set(combo["x"] for combo in combinations)
        y_values = set(combo["y"] for combo in combinations)
        
        self.assertEqual(x_values, {1, 2, 3})
        self.assertEqual(len(y_values), 3)  # 0.0, 0.5, 1.0
    
    def test_random_sampling_strategy(self):
        """测试随机采样策略"""
        strategy = RandomSampling()
        combinations = strategy.sample(self.param_space, n_samples=5)
        
        self.assertEqual(len(combinations), 5)
        
        for combo in combinations:
            self.assertIn("x", combo)
            self.assertIn("y", combo)
            self.assertIn(combo["x"], [1, 2, 3])
            self.assertGreaterEqual(combo["y"], 0.0)
            self.assertLessEqual(combo["y"], 1.0)
    
    def test_assign_sampling_strategy(self):
        """测试指定值采样策略"""
        descriptors_with_defaults = [
            ParamDescriptor(name="a", type_="int", default=10),
            ParamDescriptor(name="b", type_="float", default=0.5),
            ParamDescriptor(name="c", type_="categorical", choices=["x", "y"], default="x")
        ]
        
        param_space = ParamSpace(descriptors_with_defaults)
        strategy = AssignSampling()
        combinations = strategy.sample(param_space)
        
        self.assertEqual(len(combinations), 1)
        
        combo = combinations[0]
        self.assertEqual(combo["a"], 10)
        self.assertEqual(combo["b"], 0.5)
        self.assertEqual(combo["c"], "x")


class TestParamSpace(unittest.TestCase):
    """测试 ParamSpace 类"""
    
    def setUp(self):
        """测试前准备"""
        random.seed(42)
        
        # 创建测试用的参数描述符
        self.descriptors = [
            ParamDescriptor(
                name="window",
                type_="int",
                low=10,
                high=20,
                step=5,
                default=15
            ),
            ParamDescriptor(
                name="threshold",
                type_="float",
                low=0.1,
                high=0.5,
                step=0.2,
                default=0.3
            ),
            ParamDescriptor(
                name="strategy",
                type_="categorical",
                choices=["fast", "slow"],
                default="fast"
            )
        ]
    
    def test_param_space_creation(self):
        """测试参数空间创建"""
        param_space = ParamSpace(self.descriptors, strategy="grid")
        
        self.assertEqual(len(param_space.descriptors), 3)
        self.assertEqual(param_space.strategy, "grid")
        self.assertEqual(param_space.max_combinations, 10000)
    
    def test_grid_sampling(self):
        """测试网格采样"""
        param_space = ParamSpace(self.descriptors, strategy="grid")
        combinations = param_space.sample()
        
        # 预期组合数: 3 * 3 * 2 = 18
        expected_count = 3 * 3 * 2
        self.assertEqual(len(combinations), expected_count)
        
        # 检查第一个组合
        first_combo = combinations[0]
        self.assertIn("window", first_combo)
        self.assertIn("threshold", first_combo)
        self.assertIn("strategy", first_combo)
        
        # 检查所有组合都有完整的参数
        for combo in combinations:
            self.assertEqual(len(combo), 3)
            self.assertIn("window", combo)
            self.assertIn("threshold", combo)
            self.assertIn("strategy", combo)
    
    def test_random_sampling(self):
        """测试随机采样"""
        param_space = ParamSpace(self.descriptors, strategy="random")
        combinations = param_space.sample(n_samples=10)
        
        self.assertEqual(len(combinations), 10)
        
        # 检查每个组合的完整性
        for combo in combinations:
            self.assertEqual(len(combo), 3)
            self.assertIn("window", combo)
            self.assertIn("threshold", combo)
            self.assertIn("strategy", combo)
            
            # 检查值的范围
            self.assertGreaterEqual(combo["window"], 10)
            self.assertLessEqual(combo["window"], 20)
            self.assertGreaterEqual(combo["threshold"], 0.1)
            self.assertLessEqual(combo["threshold"], 0.5)
            self.assertIn(combo["strategy"], ["fast", "slow"])
    
    def test_assign_sampling(self):
        """测试指定值采样"""
        param_space = ParamSpace(self.descriptors, strategy="assign")
        combinations = param_space.sample()
        
        self.assertEqual(len(combinations), 1)
        
        combo = combinations[0]
        self.assertEqual(combo["window"], 15)
        self.assertEqual(combo["threshold"], 0.3)
        self.assertEqual(combo["strategy"], "fast")
    
    def test_estimate_combinations(self):
        """测试组合数估算"""
        param_space = ParamSpace(self.descriptors)
        estimated = param_space.estimate_combinations()
        
        # 预期: 3 * 3 * 2 = 18
        expected = 3 * 3 * 2
        self.assertEqual(estimated, expected)
    
    def test_max_combinations_limit(self):
        """测试最大组合数限制"""
        # 创建会产生大量组合的参数空间
        large_descriptors = [
            ParamDescriptor(name=f"param_{i}", type_="int", low=1, high=100, step=1)
            for i in range(3)
        ]
        
        param_space = ParamSpace(large_descriptors, strategy="grid", max_combinations=50)
        combinations = param_space.sample()
        
        # 应该被限制在50个以内
        self.assertLessEqual(len(combinations), 50)
    
    def test_from_config(self):
        """测试从配置创建参数空间"""
        config = {
            "params": {
                "sampling": "grid",
                "max_combinations": 100,
                "window": {
                    "type": "int",
                    "range": [10, 30],
                    "step": 10,
                    "default": 20
                },
                "threshold": {
                    "type": "float",
                    "range": [0.1, 0.9],
                    "step": 0.4,
                    "default": 0.5
                },
                "mode": {
                    "type": "categorical",
                    "choices": ["fast", "slow"],
                    "default": "fast"
                }
            }
        }
        
        param_space = ParamSpace.from_config(config)
        
        self.assertEqual(param_space.strategy, "grid")
        self.assertEqual(param_space.max_combinations, 100)
        self.assertEqual(len(param_space.descriptors), 3)
        
        # 检查参数描述符
        desc_names = [desc.name for desc in param_space.descriptors]
        self.assertIn("window", desc_names)
        self.assertIn("threshold", desc_names)
        self.assertIn("mode", desc_names)
    
    def test_unsupported_strategy(self):
        """测试不支持的策略"""
        with self.assertRaises(ValueError) as context:
            ParamSpace(self.descriptors, strategy="unsupported")
        
        self.assertIn("unsupported sampling strategy", str(context.exception))


if __name__ == "__main__":
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestParamDescriptor))
    test_suite.addTest(unittest.makeSuite(TestSamplingStrategies))
    test_suite.addTest(unittest.makeSuite(TestParamSpace))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print(f"\n{'='*60}")
    print(f"unitest/params.py 模块测试摘要:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    # 返回退出码
    sys.exit(0 if len(result.failures) == 0 and len(result.errors) == 0 else 1)
