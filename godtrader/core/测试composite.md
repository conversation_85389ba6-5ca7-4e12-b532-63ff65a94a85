# 一、任务说明

请你帮我对composite_signal.py中的CompositeSignal类进行单元测试，测试generate_param_combinations方法是否能够正确生成所有可能的参数组合、以及from_param_dict能否够正确地从参数字典中重建实例，并调用generate_signal方法生成信号值。

同时，CompositeSignal依赖LeafSignal，请你在测试的过程中也一并帮我测试LeafSignal的各项功能是否正常。

# 二、背景知识

1. CompositeSignal类设计：
    CompositeSignal继承自BaseSignal，它有两个角色：一是作为原型，接受config.yaml中的参数（使用from_config()），初始化属于它的ParamSpace。并使用ParamSpace里的设置，驱动param_space.sample()来生成一系列的实例化参数字典；二是作为实例，接受原型产生的参数字典，重建可运行的实例（使用from_param_dict()）。在本次任务中，我需要你测试它作为这两个角色的功能都是否正常。

2. CompositeSignal类依赖：
    CompositeSignal是BaseSignal抽象类的一个实现类，但是CompositeSignal有一个成员children，它是一个列表，里面可能会是其他的CompositeSignal，也可能是某些具体的Signal类如BollingerCrossMidSignal等。其中，BollingerCrossMidSignal这种类是LeafSignal的实现类，LeafSignal是继承BaseSignal的一个抽象类。目前还没有实现LeafSignal的实现类，你可能需要自己编写一些mock类来配合测试，理论上你只需要实现generate_condition，save_signal这两个方法即可，其他的过程都已经在LeafSignal类里完成了。我可以在这里给你一个粗略的参考：
    ```python
    @register_signal("MockSignal1")
    class MockSignal1(LeafSignal):
        def __init__(self, name, param_space = None, params = None):
            super().__init__(name, param_space, params)
        def generate_condition(self, df: pl.DataFrame) -> pl.Expr:
            condition = pl.col("open") - pl.col("close") > 0
            return condition
        def save_signal():
            return super().save_signal() 
    ```

3. LeafSignal类依赖：
    类似CompositeSignal，LeafSignal的实现类也有两个角色：一是作为原型，接受config.yaml中的参数（使用from_config()），初始化属于它的ParamSpace。并使用ParamSpace里的设置，驱动param_space.sample()来生成一系列的实例化参数字典；二是作为实例，接受原型产生的参数字典，重建可运行的实例（使用from_param_dict()）。而LeafSignal类依赖于Indicator，如BollingerIndicator。它们是BaseIndicator的实现类。为了测试LeafSignal，你可能也需要编写一个mock的Indicator类。在这里也给出一个简单例子：
    ```python
    @register_indicator("MockIndicator1")
    class MockIndicator1(BaseIndicator):
        def __init__(self, param_space: Optional[ParamSpace] = None, params: Optional[Dict[str,Any]] = None):
            super().__init__(param_space, params)
        def compute(self, df: pl.DataFrame) -> pl.DataFrame:
            return df
    ```

4. Config:
    为了生成Indicator、LeafSignal、CompositeSignal的原型的ParamSpace，需要在配置文件里设定相应的参数，请你参考config.yaml的写法，为本次测试构造一个测试用的config，注册好相应的Indicator、Signal和CompositeSignal。注意，无论你怎么构造CompositeSignal，CompositeSignal在config中也只有一个，因为所有的CompositeSignal都共享一套控制ParamSpace的设置，只有LeafSignal的实现类是需要一个个区分开的，因为它们依赖不同的Indicator，拥有不同的参数。

# 三、期望测试范围与结果

1. 对于CompositeSignal中的generate_param_combinations方法：期望获得以下格式的结果：
    ```python
        """
        返回：
        [
          {                       # 一条完整组合（树形）
            "type": "composite",
            "operator": "and",
            "vote_threshold": 0.5,
            "signal_name": self.name,
            "children": [
                { "type": "leaf", "signal_name": "leaf1", "indicator_params": {...}, "signal_params": {...} },
                { "type": "composite", "operator": "or", "vote_threshold":0.5, "children": [...] },
                ...
            ]
          },
          ...                     # 所有笛卡尔积
        ]
        """
    ```

2. 期望从以上参数组合中，重建出带有LeafSignal实现类为children的CompositeSignal。
3. 对于重建好的CompositeSignal，调用其generate_signal方法，期望获得一个df，格式如下：
    ```python
    """
    ┌─────────────────────┬─────────────────────┬────────────────┬────────────────┬───┬────────────────┬─────────────┬─────────────────┬────────────┐
    │ start_time          ┆ end_time            ┆ open           ┆ high           ┆ … ┆ close          ┆ vol         ┆ money           ┆ trades_cnt │
    │ ---                 ┆ ---                 ┆ ---            ┆ ---            ┆   ┆ ---            ┆ ---         ┆ ---             ┆ ---        │
    │ i64                 ┆ i64                 ┆ str            ┆ str            ┆   ┆ str            ┆ str         ┆ str             ┆ i64        │
    ╞═════════════════════╪═════════════════════╪════════════════╪════════════════╪═══╪════════════════╪═════════════╪═════════════════╪════════════╡
    在以上基础上再多加一列signal值
    """
    ```

