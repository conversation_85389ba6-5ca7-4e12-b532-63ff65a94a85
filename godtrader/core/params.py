"""
Automative Parameter Generation

Supports assign sample, grid sample, random sample.
"""

from typing import Any, Dict, List, Union, Iterator, Optional
from dataclasses import dataclass
from itertools import product
import random
from abc import ABC, abstractmethod


@dataclass
class ParamDescriptor:
    """descriptor of one param field, specifying the way you want to generate it."""
    name: str
    type_: str  # "int", "float", "categorical"
    low: Optional[Union[int, float]] = None #for int, float: range [low, high] in step. 
    high: Optional[Union[int, float]] = None
    step: Optional[Union[int, float]] = None
    choices: Optional[List[Any]] = None
    default: Optional[Any] = None # assgined specific value for a param
    
    def __post_init__(self):
        """validate param settings"""
        # assign value is legal
        if not self.low and not self.high and not self.step and not self.choices and self.default is not None:
            return

        if self.type_ in ["int", "float"]:
            if self.low is None or self.high is None:
                raise ValueError(f"low and high value must be assigned to param {self.name}")
            if self.low >= self.high:
                raise ValueError(f"param {self.name} got low > high")
        elif self.type_ == "categorical":
            if not self.choices:
                raise ValueError(f"choises must be assigned to param {self.name}")
    
    def generate_values(self, strategy: str = "grid", n_samples: int = 10) -> List[Any]:
        if self.type_ == "categorical":
            return self.choices
        
        if strategy == "grid":
            return self._generate_grid()
        elif strategy == "random":
            return self._generate_random(n_samples)
        elif strategy == "assign":
            return self._generate_assigned() 
        else:
            raise ValueError(f"unsupported sampling strategy: {strategy}")
    
    def _generate_grid(self) -> List[Any]:
        if self.step is None:
            # sample 10 points by default 
            n_steps = 10
        else:
            n_steps = int((self.high - self.low) / self.step) + 1
        
        if self.type_ == "int":
            return list(range(int(self.low), int(self.high) + 1, 
                            int(self.step) if self.step else max(1, int((self.high - self.low) / 10))))
        else:  # float
            return [self.low + i * (self.high - self.low) / (n_steps - 1) 
                   for i in range(n_steps)]
    
    def _generate_random(self, n_samples: int) -> List[Any]:
        if self.type_ == "int":
            return [random.randint(int(self.low), int(self.high)) for _ in range(n_samples)]
        else:  # float
            return [random.uniform(self.low, self.high) for _ in range(n_samples)]
    
    def _generate_assigned(self):
        return 



class ISamplingStrategy(ABC):
    """Interface of Sampling Strategies"""
    
    @abstractmethod
    def sample(self, param_space: 'ParamSpace', **kwargs) -> List[Dict[str, Any]]:
        pass

class AssignSampling(ISamplingStrategy):
    """Sample the specific one value assigned in the config instead of automative generation"""
    
    def sample(self, param_space: 'ParamSpace' , **kwargs) -> List[Dict[str, Any]]:
        params = {}
        for desc in param_space.descriptors:
            params[desc.name] = desc.default
        
        return [params]
        

class GridSampling(ISamplingStrategy):
    
    def sample(self, param_space: 'ParamSpace', **kwargs) -> List[Dict[str, Any]]:
        param_values = {}
        for desc in param_space.descriptors:
            param_values[desc.name] = desc.generate_values("grid")
        
        keys = list(param_values.keys())
        values = list(param_values.values())
        
        combinations = []
        for combo in product(*values):
            combinations.append(dict(zip(keys, combo)))
        
        return combinations


class RandomSampling(ISamplingStrategy):
    
    def sample(self, param_space: 'ParamSpace', **kwargs) -> List[Dict[str, Any]]:
        """n_samples:int"""
        n_samples:int = kwargs.get("n_samples")
        combinations = []
        for _ in range(n_samples):
            combo = {}
            for desc in param_space.descriptors:
                values = desc.generate_values("random", 1)
                combo[desc.name] = values[0]
            combinations.append(combo)
        
        return combinations



class ParamSpace:
    """ParamSpace is the manager of params in different indicators and signals, 
        which loads the param fields and specifies the way we initialize them.

        ParamSpace will not directly inject values to the param fields, instead
        it runs a sampler to generate param combinations and return dicts with all possible
        values
    """
    
    def __init__(self, descriptors: List[ParamDescriptor], 
                 strategy: str = "grid", max_combinations: int = 10000):
        self.descriptors = descriptors
        self.strategy = strategy
        self.max_combinations = max_combinations
        
        # registers the sampling strategies
        self.strategies = {
            "grid": GridSampling(),
            "random": RandomSampling(),
            "assign": AssignSampling()
        }
        
        if strategy not in self.strategies:
            raise ValueError(f"unsupported sampling strategy: {strategy}")
    
    def sample(self, **kwargs) -> List[Dict[str, Any]]:
        """kwargs:

            random:
                n_samples: Optional[int] = None
            
        """
        sampler:ISamplingStrategy = self.strategies[self.strategy]     
        if self.strategy == "grid":
            combinations = sampler.sample(self)
            # TODO: limit the param explosion
            if len(combinations) > self.max_combinations:
                random.shuffle(combinations)
                combinations = combinations[:self.max_combinations]
            return combinations
        elif self.strategy == "random":
            n_samples = kwargs.get("n_samples")
            if n_samples is None:
                n_samples = min(1000, self.max_combinations)
            return sampler.sample(self, n_samples=n_samples)
        elif self.strategy == "assign":
            return sampler.sample(self)
        else:
            return None

    
    def estimate_combinations(self) -> int:
        total = 1
        for desc in self.descriptors:
            if desc.type_ == "categorical":
                total *= len(desc.choices)
            else:
                if desc.step:
                    n_values = int((desc.high - desc.low) / desc.step) + 1
                else:
                    n_values = 10  # by default 10 sampling points
                total *= n_values
        return total
    
    def get_descriptor(self, name: str) -> Optional[ParamDescriptor]:
        for desc in self.descriptors:
            if desc.name == name:
                return desc
        return None
    
    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> 'ParamSpace':
        """initialize paramspace from config"""
        descriptors = []
        
        for param_name, param_config in config.get('params', {}).items():
            if param_name in ['type', 'sampling', 'max_combinations', 'max_children']:
                continue
                
            desc = ParamDescriptor(
                name=param_name,
                type_=param_config.get('type', 'float'),
                low=param_config.get('range', [0, 1])[0] if 'range' in param_config else None,
                high=param_config.get('range', [0, 1])[1] if 'range' in param_config else None,
                step=param_config.get('step'),
                choices=param_config.get('choices'),
                default=param_config.get('default')
            )
            descriptors.append(desc)
        
        strategy = config.get('params', {}).get('sampling', 'grid')
        max_combinations = config.get('params', {}).get('max_combinations', 10000)
        
        return cls(descriptors, strategy, max_combinations)
