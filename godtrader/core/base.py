"""
Abstract classes

"""
from code.core.log import LK #TODO: 是否封装成包？
from .params import ParamSpace

import polars as pl
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional




class BaseIndicator(ABC):
    """Abstract class of indicators (i.e MACD, BOLL, RSI, etc)
    """
    
    def __init__(self, param_space: Optional[ParamSpace] = None, params: Optional[Dict[str,Any]] = None):
        """
        Args:
            param_space: the paramspace for the origin to generate params
            params: the params for an instance to execute a backtest
        
        """
        self.param_space = param_space
        self.params = params
        self.logger = LK
    
    @abstractmethod
    def compute(self, df: pl.DataFrame) -> pl.DataFrame:
        """Compute the value of indicator in each K-line, returns expressions to do lazy calculation.
        
        
        Args:
            df: dataframe of one symbol
            
        self.params: 
            params of the indicator, including sampling frequency (1m by default)
            
        Returns:
            df_with_featrue: df with indicator values in columns
        """
        pass
    
    def _get_required_columns(self) -> List[str]:
        return ["open", "high", "low", "close", "vol"]
    
    def validate_data(self, df: pl.DataFrame) -> bool:
        required_cols = self._get_required_columns()
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            self.logger.error(f"missing cols: {missing_cols}")
            return False
        
        return True
    
    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> "BaseIndicator": 
        """Create an indicator from config
        """
        param_space = ParamSpace.from_config(config)
        return cls(param_space=param_space)
    
    @classmethod
    def from_param_dict(cls, params: Dict[str, Any]) -> "BaseIndicator":
        return cls(params=params)


class BaseSignal(ABC):
    """Abstract class of signals (i.e the conditions provided by different values of indicators/cols)
    """
    
    def __init__(self, name: str, param_space: Optional[ParamSpace] = None, params: Optional[Dict[str,Any]] = None):
        self.name = name
        self.param_space = param_space
        self.params = params
        self.logger = LK
    
    @abstractmethod
    def compute_features(self, df: pl.DataFrame) -> pl.DataFrame:
        """Compute the values of indicators that produce the signal, executed inside backtest engine
        
        Args:
            df: original dataframe
        
        self.params: 
            params of the indicator = self.params["indicator_params"]
            
        Returns:
            pl.DataFrame: df with indicator cols
        """
        pass
    
    @abstractmethod
    def generate_condition(self, df: pl.DataFrame) -> pl.Expr:
        """Generate the conditions that consist the signal
        
        Args:
            df: df with indicator cols
        
        self.params: 
            params of the signal = self.params["signal_params"]
            
        Returns:
            pl.Expr: conditions of a signal
        """
        pass
    
    @abstractmethod
    def save_signal(self, path:str, mode:str = "log"):
        """Save the signal config"""
        pass

    def generate_signal(self, df: pl.DataFrame) -> pl.DataFrame:
        """The pipline of genrating a single signal (no matter its a leaf or a composite one)
        """
        df_with_features = self.compute_features(df)
        condition = self.generate_condition(df_with_features)
        signal_col = f"{self.name}_signal"
        df_result = df_with_features.with_columns(
            condition.alias(signal_col)
        )
        return df_result
    
    def get_signal_strength(self, df: pl.DataFrame) -> pl.Expr:
        """Quantify the signal, map it into numerical space
        
        Returns:
            pl.Expr: by default 0, 1, could be overrieded to a continuous value
        """
        condition = self.generate_condition(df)
        return condition.cast(pl.Float64)
    
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        if self.param_space is None:
            return True
        
        for desc in self.param_space.descriptors:
            if desc.name not in params:
                self.logger.error(f"missing param: {desc.name}")
                return False
            value = params[desc.name]
            
            if desc.type_ == "int" and not isinstance(value, int):
                self.logger.error(f"param {desc.name} should be int, got {type(value)}")
                return False
            elif desc.type_ == "float" and not isinstance(value, (int, float)):
                self.logger.error(f"param {desc.name} should be float, got {type(value)}")
                return False
            elif desc.type_ == "categorical" and value not in desc.choices:
                self.logger.error(f"param {desc.name} value: {value} is an exception of alternatives: {desc.choices}")
                return False
            
            if desc.type_ in ["int", "float"]:
                if value < desc.low or value > desc.high:
                    self.logger.error(f"param {desc.name} value: {value} out of range [{desc.low}, {desc.high}]")
                    return False
        
        return True

    @classmethod
    @abstractmethod
    def from_config(cls, config: Dict[str, Any], **kwargs: Any) -> "BaseSignal": 
        """Create a signal origin from config

        Needs different implementation for leaf and composite signals
        """
        pass

    @classmethod
    @abstractmethod
    def from_param_dict(cls, params: Dict[str, Any]) -> "BaseSignal": 
        """Create a signal instance from specific params

        Needs different implementation for leaf and composite signals
        """
        pass
