
```python
def dfs(comp=comp1):
    # comp1.children=[leaf1, comp2]，其中comp2.children = [leaf2, leaf3]
    # comp1.oper = [+, -, *, /, &]
    for child in comp.children: 
        if child.__class__ is "LeafSignal"：
            inidcators:list(BaseIndicator) = child.indicator.clone() #这里可能就得返回一个list的不同参indicator。
            for indicator in indicators:
                child_list:list(LeafSignal) = child.clone(indicator)
            
            #接下来怎么来？该return点什么？还有怎么组装operator？假设comp1的paramspace中已经规定好了有哪些可选的
            return 
        elif child.__class__ is "CompostieSignal":
            for c in child.children:
                dfs(c)
```

```python
#在backtest engin里
# 入参有signals: List(CompositeSignal)
for comp in signals:
    for combo in comp.clone(): #产生一组从indicator到leaf signal到operator都确定的实例
        worker_execute(df, combo)

```