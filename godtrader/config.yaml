# 指标配置
indicators:
  BollingerIndicator:
    params:
      sampling: "grid"          # 该指标使用网格搜索
      window:
        type: "int"
        range: [10, 30]
        step: 5
      num_std:
        type: "float"
        range: [1.5, 2.5]
        step: 0.25

# 信号配置
signals:
  BollingerCrossMidSignal:
    indicator: BollingerIndicator
    params:
      sampling: "random"        # 该信号使用随机采样
      lookback:
        type: "int"
        range: [3, 9]
        step: 1
      tolerance:
        type: "float"
        range: [0.005, 0.02]
        step: 0.005

  BollingerBandwidthSignal:
    indicator: BollingerIndicator
    params:
      sampling: "random"
      lookback:
        type: "int"
        range: [3, 15]
        step: 2
      threshold_pct:
        type: "float"
        range: [0.1, 0.5]
        step: 0.1

  BollingerBreakoutSignal:
    indicator: BollingerIndicator
    params:
      sampling: "grid"
      direction:
        type: "categorical"
        choices: ["long", "short", "both"]
      volume_confirm:
        type: "categorical"
        choices: [true, false]

#组合配置
compositions:
  params:
    max_children: 
      type: "int"
      default: 3      # 每个组合最多包含的信号数
    operators:                    # 支持的组合操作符
      type: "categorical"
      choices: ["and", "or", "vote"]

    vote_threshold: #TODO: 仅在vote模式下发挥作用，但是有点难组合到operator里去，暂时不允许自动生成,后续考虑给paramdescriptor支持tuple？
      type: "float"
      default: 0.5
