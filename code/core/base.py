import sys
from abc import ABC, abstractmethod
import functools
from inspect import iscoroutinefunction

def self_check_initialized(func):
    @functools.wraps(func)
    def sync_wrapper(self, *args, **kwargs):
        if not self.initialized:
            # print(f"Blocked: {self.__class__.__name__}.{func.__name__} (self.initialized=False)")
            return None
        return func(self, *args, **kwargs)

    @functools.wraps(func)
    async def async_wrapper(self, *args, **kwargs):
        if not self.initialized:
            # print(f"Blocked: {self.__class__.__name__}.{func.__name__} (self.initialized=False)")
            return None
        return await func(self, *args, **kwargs)

    return async_wrapper if iscoroutinefunction(func) else sync_wrapper


class ClassBase(ABC):
    def _get_class_function_name(self):
        return f"{self.__class__.__name__}.{sys._getframe(1).f_code.co_name}" # _getframe(1) -> caller frame