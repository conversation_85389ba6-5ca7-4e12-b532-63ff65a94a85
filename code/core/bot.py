from .httptool import async_post_request
from .config import CONFIG
from .log import LK

import hashlib, hmac
import time, json
import base64
import requests
from requests.exceptions import RequestException


BOTS = {}


class BaseBot():
    def send(self, text: str):
        raise NotImplementedError("This method should be implemented by subclasses.")
    async def send(self, text: str):
        raise NotImplementedError("This method should be implemented by subclasses.")



class FeishuBot(BaseBot):
    def __init__(self, name, webhook_url, secret, label):
        self.name = name
        self.webhook_url = webhook_url
        self.secret = secret
        self.label = label
        
    def _sign(self, timestamp, secret):
        string_to_sign = '{}\n{}'.format(timestamp, secret)
        hmac_code = hmac.new(string_to_sign.encode("utf-8"), digestmod=hashlib.sha256).digest()
        sign = base64.b64encode(hmac_code).decode('utf-8')
        return sign

    async def send(self, text: str, label: dict=None):
        if label is None:
            label = self.label
        try:
            timestamp = str(int(time.time()))
            sign = self._sign(timestamp, self.secret)
            headers = {"Content-Type": "text/plain"}
            payload = {
                "timestamp": timestamp,
                "sign": sign,
                "msg_type": "text",
                "content": {
                    "text": f"{json.dumps(label)}\n{text}"
                }
            }
            response = await async_post_request(url=self.webhook_url, headers=headers, data=payload)
            if response.code > 300:
                LK.error(f"Bot ({self.name}) send failed, due to HTTP({response.code}): {response.reason}", f_lvl=1)
            return
        except Exception as e:
            LK.error(f"Bot ({self.name}) send failed, due to Exception:\n {LK._format_exception(e)}", f_lvl=1)

    def send(self, text: str, label: dict=None):
        if label is None:
            label = self.label
        try:
            timestamp = str(int(time.time()))
            sign = self._sign(timestamp, self.secret)
            headers = {"Content-Type": "text/plain"}
            payload = {
                "timestamp": timestamp,
                "sign": sign,
                "msg_type": "text",
                "content": {
                    "text": f"{json.dumps(label)}\n{text}"
                }
            }
            response = requests.post(
                url=self.webhook_url,
                headers=headers,
                json=payload,
                timeout=15
            )
            if response.status_code >= 300:
                LK.error(f"Bot ({self.name}) send failed, due to HTTP({response.status_code}): {response.reason}", f_lvl=1)
            return
        except Exception as e:
            LK.error(f"Bot ({self.name}) send failed, due to Exception:\n {str(e)}", f_lvl=1)




def get_bot(bot_name: str):
    global BOTS
    if bot_name not in CONFIG.get("BOT", {}):
        raise ValueError(f"Bot ({bot_name}) is not configured.")
    if bot_name in BOTS:
        return BOTS[bot_name]
    BOT_CFG = CONFIG["BOT"][bot_name]
    BOTS[bot_name] = FeishuBot(
        name= bot_name,
        webhook_url= BOT_CFG.get("URL", "invalid_url"),
        secret= BOT_CFG.get("SECRET", "invalid_secret"),
        label=BOT_CFG.get("LABEL", {}),
    )
    return BOTS[bot_name]



class _SourceModule:
    def __call__(self, bot_name: str):
        return get_bot(bot_name)
import sys
sys.modules[__name__] = _SourceModule()