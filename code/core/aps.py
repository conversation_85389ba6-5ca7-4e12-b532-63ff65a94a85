from .log import L<PERSON>
from .config import <PERSON><PERSON><PERSON>

import async<PERSON>
from datetime import datetime
from tornado.ioloop import <PERSON><PERSON><PERSON>
from apscheduler.schedulers.tornado import TornadoScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from typing import Dict, Any, Callable


class TaskBase():
    def __init__(self, 
                    name: str = None, 
                    id: str = None, 
                    func: Callable = None,
                    args: Dict[str, Any] = None):
        self.name = name if name is not None else "default_task"
        self.id = id if id is not None else f"{self.name}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        if func is None:
            raise ValueError("func must be provided")
        self.func = func
        self.args = args if args is not None else {}
        self.trigger = None

    def __str__(self):
        return f"""Task {self.name}({self.id}): {self.trigger}"""
        # Function: {self.func.__name__}, Args: {self.args}
        # """

class IntervalTask(TaskBase):
    def __init__(self, seconds: int, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.trigger = IntervalTrigger(seconds=seconds)

class CrontabTask(TaskBase):
    def __init__(self, cron_expression: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.trigger = self._parse_cron_expression(cron_expression)
    
    def _parse_cron_expression(self, cron_expression: str):
        params = cron_expression.split(" ")
        n = len(params)
        if n == 7: 
            if params[6] not in  ["UTC", "Asia/Shanghai"]:
                raise ValueError("Timezone must be 'UTC' or 'Asia/Shanghai'")
            return CronTrigger(
                second=params[0],
                minute=params[1],
                hour=params[2],
                day=params[3],
                month=params[4],
                year=params[5],
                timezone=params[6]
            )
        elif n == 8:
            if params[7] not in ["UTC", "Asia/Shanghai"]:
                raise ValueError("Timezone must be 'UTC' or 'Asia/Shanghai'")
            return CronTrigger(
                second=params[0],
                minute=params[1],
                hour=params[2],
                day_of_week=params[3],  # 使用day_of_week替代day
                month=params[4],
                year=params[5],
                timezone=params[7]  # 时区位置后移
            )
        else: 
            raise ValueError("Cron expression must have 7 or 8 fields: second, minute, hour, (opitonal day of week), day, month, year, timezone")


class APS:
    def __init__(self):
        self.scheduler = TornadoScheduler()
        self.task_di = {}

    def start(self):
        self.scheduler.start()
    def shutdown(self):
        self.scheduler.shutdown()
        LK.info("APS shutdown successfully.")

    async def _run_async_func(self, func: Callable, args: Dict[str, Any]):
        if asyncio.iscoroutinefunction(func):
            await func(**args)
        else:
            # if sync function, run in executor
            await IOLoop.current().run_in_executor(None, lambda: func(**args))

    def add_task(self, t: TaskBase):
        try:
            if not isinstance(t, IntervalTask) and not isinstance(t, CrontabTask):
                raise TypeError("Task must be an instance of IntervalTask or CrontabTask")

            if t.id in self.task_di:
                LK.warn(f"task {t.name}({t.id}) already exists, updating it.")
                self.remove_task(t.id)

            async def wrapper():
                await self._run_async_func(t.func, t.args.copy()) # use copy to avoid mutation issues

            self.scheduler.add_job(
                wrapper,
                t.trigger,
                id=t.id,
                # args=list(t.args.values()), # args is not used here, since we wrapper the function
            )
            self.task_di[t.id] = t
        except Exception as e:
            LK.error(f"failed to add task {t.name}({t.id}), due to:\n{LK._format_exception(e)}")
        LK.info(f"add task {t.name}({t.id}) with trigger {t.trigger}")

    def remove_task(self, task_id: str):
        try:
            if task_id not in self.task_di:
                LK.warn(f"task id: ({t.id}) does not exist, cannot remove.")
                return
            t = self.task_di[task_id]
            self.scheduler.remove_job(task_id)
            del self.task_di[task_id]
            LK.info(f"remove task {t.name}({t.id})")
        except Exception as e:
            LK.error(f"failed to remove task {t.name}({t.id}), due to:\n{LK.format_exception(e)}")


SCHE = APS()
