from enum import Enum, IntEnum, auto

class RunMode(IntEnum):
    UNKNOW = 0
    UAT = 1
    PROD = 2


class DataSource(IntEnum):
    NONE = 0
    OKX = 1
    BINANCE_SPOT = 2
    BINANCE_PERP = 3
    TOKEN_INSIGHT = 4


    def __str__(self):
        return f"{self.name}"


class Action(Enum):
    NOT_SUPPORT = 0
    GET_BASIC_INFO = 1
    GET_KLINE = 2
    GET_HISTORY_KLINE = 3
    GET_MARKETCAP = 4

    SUB_KLINE = 1001

    def __str__(self):
        return f"{self.name}"


class ErrorCode(IntEnum):
    OK = 0
    ERROR = -1
    # internal error
    INTERNAL_ERROR = -3001
    ACTION_NOT_SUPPORT = -3002
    CONNECTIONS_NOT_AVAILABLE = -3003
    PARAM_NOT_SUPPORT = -3004
    # external error
    EXTERNAL_ERROR = -4000
    RATE_LIMIT = -4001


    def __str__(self):
        return f"{self.name}({self.value})"


class SymbolType(IntEnum):
    NONE = 0
    SPOT = 1            # 现货
    MARGIN = 2          # 杠杆交易
    PERP_LINEAR = 3     # 正向永续合约
    PERP_INVERSER = 4   # 反向永续合约
    FUTURE_LINEAR = 5   # 正向交割合约
    FUTURE_INVERSER = 6 # 反向交割合约
    OPTION = 7          # 期权

    def __str__(self):
        return f"{self.name}"


class SymbolStatus(IntEnum):
    UNKNOW = 0
    TRADING = 1         # 正常交易（连续竞价
    AUCTION = 2         # 集合竞价
    CLOSE = 3           # 收盘
    BREAK = 4           # 暂停交易
    HALT = 5            # 下线

    def __str__(self):
        return f"{self.name}"

