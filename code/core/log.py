from .httptool import async_post_request
from .base import self_check_initialized

import inspect, pytz, io, time, sys, os
from datetime import datetime
import traceback
from typing import Dict
import logging
from logging.handlers import TimedRotatingFileH<PERSON>ler, RotatingFileHandler



def get_local_logger(filedir:str, filename:str, local_log_level) -> logging.Logger:
    if local_log_level == "DEBUG":
        lvl = logging.DEBUG
    elif local_log_level == "INFO":
        lvl = logging.INFO
    elif local_log_level == "WARNING":
        lvl = logging.WARNING
    elif local_log_level == "ERROR":
        lvl = logging.ERROR
    elif local_log_level == "CRITICAL":
        lvl = logging.CRITICAL
    else:
        lvl = logging.INFO
    prefix = os.path.join(filedir, 'logs')
    os.makedirs(prefix, exist_ok=True)
    filepath = os.path.join(prefix, f"{filename}.log")
    # log_handler = RotatingFileHandler(filepath, maxBytes=2048 * 1024 * 1024, backupCount=7)
    log_handler = TimedRotatingFileHandler(filepath, backupCount=7, when='D')
    logger = logging.getLogger(name=filepath)
    logger.setLevel(lvl)
    logger.addHandler(log_handler)
    return logger


class Loki():
    class BufferManager():
        def __init__(self, buf_len:int=40, buf_size:int=1_000_000):
            self.sample_di: dict = {}
            self.buffers: list = []
            self.buffer_used: int = 0
            self.MAX_BUFFER_LEN = buf_len
            self.MAX_BUFFER_SIZE = buf_size

            self.failed_c = 0
            self.failed_s = 0

        def reset_buf(self):
            self.buffers = []
            self.buffer_used = 0
        def push_msg(self, msg:str) -> str:
            res = ""
            self.buffers.append(msg)
            self.buffer_used += len(msg)
            if len(self.buffers) >= self.MAX_BUFFER_LEN or self.buffer_used >= self.MAX_BUFFER_SIZE:
                res = "\n".join(self.buffers)
                self.reset_buf()
            return res
        def reach_max_failed_cnt(self, add:int):
            cur_s = int(time.time())
            # reset every 3 hours
            if cur_s - self.failed_s > 10800:
                self.failed_c = 0
                self.failed_s = cur_s
            # skip snd if failed over 10 times
            if self.failed_c > 10:
                return True
            self.failed_c += add
            return False


    BUFFER: Dict[str, BufferManager] = {}
    LOKI_URLS: Dict[str, str] = {}
    ONLY_LOCAL: Dict[str, bool] = {}
    LOCAL_LOGGERS: Dict[str, logging.Logger] = {}
    LABELS: Dict[str, dict] = {}
    timezone = pytz.timezone("UTC")
    headers = {'Content-Type': 'application/json'}


    def __init__(self):
        self.initialized = False

    def init(self, cli_name:str, loki_url:str, labels:dict, 
             local_log_level:str, filedir:str, filename:str, only_local:bool, 
             buf_len:int=40, buf_size:int=1_000_000):
        self.cli_name = cli_name
        self.loki_url = f"{loki_url.rstrip('/')}/loki/api/v1/push"
        self.label = labels
        self.only_local = only_local
        self.local_log_level = local_log_level
        self.local_log = get_local_logger(filedir, filename, local_log_level)

        self.BUFFER[cli_name] = self.BufferManager(buf_len=buf_len, buf_size=buf_size)
        self.LOKI_URLS[cli_name] = self.loki_url
        self.ONLY_LOCAL[cli_name] = only_local
        self.LOCAL_LOGGERS[cli_name] = self.local_log
        self.LABELS[cli_name] =self.label
        self.initialized = True

    def _format_msg(self, msg:str, levelname:str, funcname:str, pathname:str, lineno:str):
        timestamp = datetime.now(self.timezone).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        return f"[{timestamp}] {levelname}[{funcname}]: {msg} [in {pathname}:{lineno}]"

    def _format_exception(self, ei):
        if isinstance(ei, BaseException):
            ei = (type(ei), ei, ei.__traceback__)
        elif not isinstance(ei, tuple):
            ei = sys.exc_info()
        sio = io.StringIO()
        tb = ei[2]
        traceback.print_exception(ei[0], ei[1], tb, None, sio)
        s = sio.getvalue()
        sio.close()
        if s[-1:] == "\n":
            s = s[:-1]
        return s

    def _get_frame_info(self, f_lvl:int=1):
        frame = inspect.currentframe().f_back.f_back # align with user function call stack
        f_lvl = 0 if f_lvl < 0 else f_lvl
        for _ in range(f_lvl):
            frame = frame.f_back
            if frame is None:
                return "None", "None", "None"
        first_arg = frame.f_locals.get('self') or frame.f_locals.get('cls')
        class_name = f"{first_arg.__class__.__name__}." if first_arg else ""
        funcname = f"{class_name}{frame.f_code.co_name}"
        pathname = frame.f_code.co_filename
        lineno = frame.f_lineno
        return funcname, pathname, lineno

    async def _snd_payload(self, hb_msg:str, labels:dict, at_once=False):
        if at_once:
            msg = hb_msg
        else:
            msg = self.BUFFER[self.cli_name].push_msg(hb_msg)
            if len(msg) == 0: return

        if self.BUFFER[self.cli_name].reach_max_failed_cnt(0):
            self.local_log.warning(f"loki cli ({self.cli_name}) reach max failed cnt, skip snd ...")
            return
        loki_ts = str(int(datetime.now(self.timezone).timestamp() * 1e9))
        payload = {
            "streams": [
                {
                    "stream": labels,
                    "values": [[loki_ts, msg]]
                }
            ]
        }
        if self.only_local:
            self.local_log.info(msg)
            return
        response = await async_post_request(self.loki_url, headers=self.headers, data=payload)
        if response.code >= 400:
            raise Exception(f"HTTP[{response.code}] {str(response.reason)}\n{str(response.body)}")

    async def _snd_log(self, msg:str, logtype:str, labels:dict=None, f_lvl:int=1, at_once=False):
        try:
            if not labels:
                labels = self.label
            funcname, pathname, lineno = self._get_frame_info(f_lvl)
            hb_msg = self._format_msg(msg, logtype, funcname, pathname, lineno)
            await self._snd_payload(hb_msg, labels, at_once)
        except Exception as e:
            fe = self._format_exception(e)
            self.local_log.critical(fe)
            # self.local_log.info(f"loki write it to local:\n{msg}") # plz specifically localize the vital msg in you own code, loki will ignore msg when network error
            try:
                await self._snd_payload(fe, labels, at_once=True)
            except:
                self.BUFFER[self.cli_name].reach_max_failed_cnt(1)
                self.local_log.critical(f"loki cli ({self.cli_name}) failed to send error msg due to network error")


    # user function
    @self_check_initialized
    async def error(self, msg:str, labels:dict=None, f_lvl:int=1):
        # TODO: IOLoop.current().spawn_callback
        await self._snd_log(msg, "ERROR", labels, f_lvl)

    @self_check_initialized
    async def warn(self, msg:str, labels:dict=None, f_lvl:int=1):
        # TODO: IOLoop.current().spawn_callback
        await self._snd_log(msg, "WARN", labels, f_lvl)

    @self_check_initialized
    async def info(self, msg:str, labels:dict=None, f_lvl:int=1):
        # TODO: IOLoop.current().spawn_callback
        await self._snd_log(msg, "INFO", labels, f_lvl)

    @self_check_initialized
    def debug(self, msg:str, labels:dict=None, f_lvl:int=1):
        funcname, pathname, lineno = self._get_frame_info(f_lvl)
        self.local_log.debug(msg=self._format_msg(msg, "DEBUG", funcname, pathname, lineno))

    @self_check_initialized
    def info(self, msg:str, labels:dict=None, f_lvl:int=1):
        funcname, pathname, lineno = self._get_frame_info(f_lvl)
        self.local_log.info(msg=self._format_msg(msg, "INFO", funcname, pathname, lineno))

    @self_check_initialized
    def critical(self, msg:str, labels:dict=None, f_lvl:int=1):
        funcname, pathname, lineno = self._get_frame_info(f_lvl)
        self.local_log.critical(msg=self._format_msg(msg, "CRITICAL", funcname, pathname, lineno))

    @self_check_initialized
    def warn(self, msg:str, labels:dict=None, f_lvl:int=1):
        funcname, pathname, lineno = self._get_frame_info(f_lvl)
        self.local_log.warning(msg=self._format_msg(msg, "WARN", funcname, pathname, lineno))

    @self_check_initialized
    def error(self, msg:str, labels:dict=None, f_lvl:int=1):
        funcname, pathname, lineno = self._get_frame_info(f_lvl)
        self.local_log.error(msg=self._format_msg(msg, "ERROR", funcname, pathname, lineno))


    @self_check_initialized
    async def sample(self, msg:str, user_key:str, labels:dict=None, f_lvl:int=1, logtype:str="INFO", interval:int=300):
        funcname, _, lineno = self._get_frame_info(0)
        key = f"{user_key}|{funcname}({lineno})"
        sample_label = labels.copy()
        sample_label["SAMPLE"] = key[:32]
        cur_s = int(time.time())
        if key in self.BUFFER[self.cli_name].sample_di:
            if cur_s - self.BUFFER[self.cli_name].sample_di[key] < interval:
                return
        self.BUFFER[self.cli_name].sample_di[key] = cur_s
        await self._snd_log(msg, logtype, sample_label, f_lvl, True)


    @classmethod
    @self_check_initialized
    async def flush(cls):
        async def _snd(cli_name, loki_url, headers, timezone, labels, msg, only_local, local_log):
            try:
                loki_ts = str(int(datetime.now(timezone).timestamp() * 1e9))
                payload = {
                    "streams": [
                        {
                            "stream": labels,
                            "values": [[loki_ts, msg]]
                        }
                    ]
                }
                if only_local:
                    local_log.info(msg)
                    return
                response = await async_post_request(loki_url, headers=headers, data=payload)
                if response.code >= 400:
                    raise Exception(f"HTTP[{response.code}] {str(response.reason)}\n{str(response.body)}")
            except Exception as e:
                local_log.critical(f"loki cli ({cli_name}) failed to flush to {loki_url} due to {str(e)}")
                # local_log.info(f"loki write it to local:\n{msg}") # plz specifically localize the vital msg in you own code, loki will ignore msg when network error

        for cli_name in cls.BUFFER.keys():
            if len(cls.BUFFER[cli_name].buffers) > 0:
                cls.LOCAL_LOGGERS[cli_name].info(f"loki cli ({cli_name}) start to flush to {cls.LOKI_URLS[cli_name]}")
                msg = "\n".join(cls.BUFFER[cli_name].buffers)
                cls.BUFFER[cli_name].reset_buf()
                if cls.BUFFER[cli_name].reach_max_failed_cnt(0):
                    cls.LOCAL_LOGGERS[cli_name].warn(f"loki cli ({cli_name}) reach max failed cnt, skip snd ...")
                    return
                await _snd(cli_name, cls.LOKI_URLS[cli_name], cls.headers, cls.timezone, cls.LABELS[cli_name], msg, cls.ONLY_LOCAL[cli_name], cls.LOCAL_LOGGERS[cli_name])



LK = Loki()