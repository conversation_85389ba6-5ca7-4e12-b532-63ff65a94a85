import subprocess
from typing import List

from .log import LK

INTERACTIVE_COMMANDS = ["vim", "nano", "top", "htop", "ssh"]
def is_interactive_command(command: str) -> bool:
    return any(cmd in command for cmd in INTERACTIVE_COMMANDS)

def get_command_output(str_li: List[str], parser: callable = None) -> str:
    command = str_li
    if is_interactive_command(command):
        raise ValueError("Interactive commands are not allowed.")
    try:
        result = subprocess.run(command, shell=True, check=True,
                                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                stdin=subprocess.DEVNULL,
                                text=False)
        output = result.stdout
        try:
            decoded_output = output.decode('utf-8')
        except UnicodeDecodeError:
            decoded_output = output.decode('ISO-8859-1', errors='replace')
        if parser is not None:
            return parser(output)
        return decoded_output
    except subprocess.CalledProcessError as e:
        LK.error(f"ERROR({e.stderr.strip()})")
        return None
    except UnicodeDecodeError as e:
        LK.error(f"ERROR(decoding error: " + str(e) + ")")
        return None



