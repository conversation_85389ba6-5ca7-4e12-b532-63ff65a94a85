from .log import LK

from typing import Union, Dict, Any, Optional
import os, time, ssl, json, asyncio
from abc import ABC, abstractmethod
import tornado.websocket
from tornado.websocket import WebSocketClientConnection, websocket_connect
from tornado.ioloop import I<PERSON>oop
from datetime import datetime, timedelta


class WsClientBase(ABC):
    conns_pool = {}
    available_conns = {}
    
    def __init__(self, name:str, url:str, ssl:Optional[Dict] = None, pool_name:str = "default"):
        self.name = name
        self.pool_name = pool_name
        self.url = url
        self.ws:WebSocketClientConnection = None
        if ssl:
            self.ssl_flag = True
            self.ssl = ssl

        self._is_connected = -1
        # setup disconnect on idle
        self.disconnect_on_idle = True
        self.last_activity_time = datetime.now()
        self.max_idle_time = timedelta(minutes=5)
        # setup conns_pool
        self.pending_futures:Dict[asyncio.Future] = {}
        if pool_name not in self.conns_pool:
            self.conns_pool[pool_name] = {}
            self.conns_pool[pool_name]["latch"] = asyncio.Lock() # TODO: raise error when conn's name is latch
        self.conns_pool[pool_name][name] = self
        # setup available_conns
        if pool_name not in self.available_conns:
            self.available_conns[pool_name] = set()
        self.available_conns[pool_name].add(name) # TODO: hash-table, you might want to keep it thread-safe

    def __str__(self):
        return f"ws[{self.name}] connected({self.is_connected}) url({self.url})"
    @property
    def is_connected(self):
        return self._is_connected > 0
    @classmethod
    async def check(cls):
        current_time = datetime.now()
        for pool_name, pool in cls.conns_pool.items():
            msg = f"[{pool_name}] {pool['latch']}\n"
            for conn_name, conn in pool.items():
                if conn_name in ["latch"]:
                    continue
                idle_time = current_time - conn.last_activity_time
                if conn._is_connected and conn.disconnect_on_idle and idle_time > conn.max_idle_time:
                    LK.warn(f"({conn_name}) {conn} shut down for idle {idle_time}s")
                    await conn.stop()
                else:
                    await conn.test_connectivity()
                msg += f"({conn_name}) {conn}\n"
            LK.critical(msg)

    @classmethod
    async def get_conn(cls, pool_name:str = "default", conn_name:str = None):
        if pool_name not in cls.conns_pool or pool_name not in cls.available_conns:
            return None
        conns = cls.conns_pool[pool_name]
        availables = cls.available_conns[pool_name]
        if conn_name is not None:
            if conn_name not in conns or conn_name not in availables:
                return None
        else:
            if not availables:
                return None
            async with conns["latch"]:
                conn_name = availables.pop()
        # connect or re-connect if needed
        if not conns[conn_name].is_connected:
            if await conns[conn_name].connect() is False:
                return None
        conns[conn_name].last_activity_time = datetime.now()
        return conns[conn_name]

    async def release(self):
        if self.pool_name not in self.conns_pool:
            return
        conns = self.conns_pool[self.pool_name]
        if self.name not in conns:
            return
        async with conns["latch"]:
            self.available_conns[self.pool_name].add(self.name)


    async def stop(self):
        if self._is_connected <= 0:
            return False
        if self.ws is not None:
            try:
                # TODO: check if there any task undone
                self.ws.close()
                await self.on_disconnected()
            except Exception as e:
                print(f"Error closing {str(self)}: {e}")

    async def connect(self):
        try:
            if self._is_connected == 0:
                return False
            if self._is_connected >= 1:
                return True

            self._is_connected = 0
            self.ws = await websocket_connect(self.url) # TODO: add ssl setting (maybe)
            await self.on_connected()
            return True
        except Exception as e:
            await self.on_disconnected()
            return False


    async def on_connected(self):
        # TODO maybe log it
        self._is_connected = 1
        print(f"CONNECT {str(self)}")
        # 1. run once
        try:
            await self.user_on_connect()
        except Exception as e:
            # TODO: log it
            pass
        # 2. enable loop run server handler
        IOLoop.current().spawn_callback(self.recv_message)

    async def on_disconnected(self):
        try:
            await self.user_on_disconnect()
        except Exception as e:
            # TODO: log it
            pass
        self._is_connected = -1
        # TODO maybe log it
        print(f"DISCONNECT {str(self)}")
        self.ws = None


    async def snd_message(self, msg:Union[str, Dict[str, Any]], msg_id:Union[str, int] = None):
        if self.ws is None:
            raise ConnectionError(f"CONNECTION_NOT_SET {str(self)}")
        if not msg:
            return
        if isinstance(msg, dict):
            try:
                msg_str = json.dumps(msg)
            except (TypeError, ValueError) as e:
                raise ValueError(f"Failed to serialize dict to JSON: {e}")
        elif isinstance(msg, str):
            msg_str = msg
        else:
            raise ValueError("Message type must be dict or str")
        await self.ws.write_message(msg_str) # TODO: maybe need to add timeout
        if msg_id is not None:
            loop = asyncio.get_running_loop()
            future = loop.create_future()
            self.pending_futures[msg_id] = future
            return await future

    async def recv_message(self):
        while True:
            if not self.is_connected: break
            try:
                msg = await self.ws.read_message()
                if msg is None:
                    raise tornado.websocket.WebSocketClosedError
                await self.handle_ws_message(msg)
            except tornado.websocket.WebSocketClosedError:
                await self.on_disconnected()
                break
            except Exception as e:
                # TODO: log it
                print(e)

    # user define function
    @abstractmethod
    async def test_connectivity(self):
        raise NotImplementedError("test_connectivity must be implemented in subclass")
    @abstractmethod
    async def handle_ws_message(self, msg):
        raise NotImplementedError("handle_ws_message must be implemented in subclass")

    async def user_on_connect(self):
        pass

    async def user_on_disconnect(self):
        pass


'''
TODO
 - long live connection test
 - reconnect test
'''

