import yaml, json

class ConfigManager():
    def __init__(self):
        self.filepath = ""
        self._data = {}

    def save_config_to_file(self, filepath:str):
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                yaml.dump(self._data, f, allow_unicode=True)
        except:
            raise Exception(f"Failed to save config to file [{filepath}].")

    def get_config_from_file(self, filepath:str):
        self.filepath = filepath
        try:
            with open(filepath, "r", encoding="utf-8") as f:
                config_file = f.read()
            cfg = yaml.load(config_file, Loader=yaml.FullLoader)
            if not cfg:
                raise ValueError(f"Config file [{filepath}] is empty or invalid.")
            self._data = cfg
            return cfg
        except FileNotFoundError:
            raise FileNotFoundError(f"Configu file [{filepath}] is not found.")

    def get(self, key, default=None):
        return self._data.get(key, default)
    def __getitem__(self, key):
        return self._data.get(key, None)
    def __setitem__(self, key, value):
        self._data[key] = value
    def __str__(self):
        return f"{json.dumps(self._data, ensure_ascii=False)}"


CONFIG = ConfigManager()