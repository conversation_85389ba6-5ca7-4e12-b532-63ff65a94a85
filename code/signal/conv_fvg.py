import sys
sys.path.append("/home/<USER>/python-workspace/3rdMar/code")

import numpy as np
import polars as pl
from quant import BaseSignal
from core.log import LK

class ConvFvgSignal(BaseSignal):
    """
    支持 1/15/30/60 min 的卷积模板匹配
    模板示例：
        long 模板 [3,1,3,1,3]
        short模板 [1,3,1,3,1]
    """
    _ALLOW_FREQ = {"1min", "15min", "30min", "60min"}

    def __init__(self,
                 template_long: list[float] = [],
                 template_short: list[float] = [],
                 threshold: float = 0.85,
                 min_gap_rate: float = 0.0005,
                 tolerance: float = 0.0005):
        super().__init__()
        self.tpl_long   = np.array(template_long,  dtype=float)
        self.tpl_short  = np.array(template_short, dtype=float)
        self.threshold  = threshold
        self.min_gap    = min_gap_rate
        self.tol        = tolerance

    # ---------- 工具 ----------
    def _normalize(self, x: np.ndarray) -> np.ndarray:
        return (x - x.min()) / (x.max() - x.min() + 1e-12)

    def _corr(self, x: np.ndarray, y: np.ndarray) -> float:
        if len(x) != len(y):
            return np.nan
        return np.corrcoef(x, y)[0, 1]

    # ---------- 重采样 ----------
    def _resample(self, df: pl.DataFrame, rule: str) -> pl.DataFrame:
        """把 1 min 重采样成 rule"""
        if rule == "1min":
            return df  # 原样返回
        rule = rule[:-2]
        bucket = pl.col("datetime").dt.truncate(rule)
        return (
            df.with_columns(bucket.alias("bucket"))
              .group_by("bucket")
              .agg(
                  pl.col("open").first().alias("open"),
                  pl.col("high").max().alias("high"),
                  pl.col("low").min().alias("low"),
                  pl.col("close").last().alias("close"),
              )
              .sort("bucket")
              .with_columns(pl.col("bucket").alias("datetime"))
        )

    # ---------- FVG 扫描 ----------
    def _scan_fvg(self, df: pl.DataFrame) -> pl.DataFrame:
        """在低频表上扫 FVG 并返回 mid/up/dn/dir"""
        df = df.with_columns(
            # Long FVG
            pl.when(pl.col("low") > pl.col("high").shift(2))
            .then((pl.col("low") + pl.col("high").shift(2)) / 2)
            .otherwise(None).alias("mid_long"),

            pl.when(pl.col("low") > pl.col("high").shift(2))
            .then(pl.col("high").shift(2)).otherwise(None).alias("up_long"),

            pl.when(pl.col("low") > pl.col("high").shift(2))
            .then(pl.col("low")).otherwise(None).alias("dn_long"),

            # Short FVG
            pl.when(pl.col("high") < pl.col("low").shift(2))
            .then((pl.col("high") + pl.col("low").shift(2)) / 2)
            .otherwise(None).alias("mid_short"),

            pl.when(pl.col("high") < pl.col("low").shift(2))
            .then(pl.col("high")).otherwise(None).alias("up_short"),

            pl.when(pl.col("high") < pl.col("low").shift(2))
            .then(pl.col("low").shift(2)).otherwise(None).alias("dn_short"),
        )

        # 过滤微小 gap
        longs = (
            df.filter(pl.col("mid_long").is_not_null())
              .with_columns(((pl.col("dn_long") - pl.col("up_long")) / pl.col("up_long")).alias("gap"))
              .filter(pl.col("gap") >= self.min_gap)
              .select(
                  pl.col("datetime").alias("born"),
                  pl.col("mid_long").alias("mid"),
                  pl.col("up_long").alias("up"),
                  pl.col("dn_long").alias("dn"),
                  pl.lit("long").alias("dir"),
              )
        )
        shorts = (
            df.filter(pl.col("mid_short").is_not_null())
              .with_columns(((pl.col("up_short") - pl.col("dn_short")) / pl.col("dn_short")).alias("gap"))
              .filter(pl.col("gap") >= self.min_gap)
              .select(
                  pl.col("datetime").alias("born"),
                  pl.col("mid_short").alias("mid"),
                  pl.col("up_short").alias("up"),
                  pl.col("dn_short").alias("dn"),
                  pl.lit("short").alias("dir"),
              )
        )
        return pl.concat([longs, shorts]).sort("born")

    # ---------- 主函数 ----------
    def gen(self, df: pl.DataFrame, freq: str = "15min") -> list[dict]:
        if freq not in self._ALLOW_FREQ:
            raise ValueError(f"freq must be in {self._ALLOW_FREQ}")

        # 1. 重采样
        lf_df = self._resample(df, freq)
        # 2. FVG 表
        fvg_df = self._scan_fvg(lf_df)

        # 3. 把 FVG mid 反填到 1 min 表
        df = df.with_columns(pl.col("close").cast(pl.Float64))
        df = df.sort("datetime")
        fvg_df = fvg_df.sort("born")

        # 用 asof 找到每根 1 min 对应的最近 FVG
        df = df.join_asof(
            fvg_df.select("born", "mid", "dir"),
            left_on="datetime",
            right_on="born",
            strategy="backward",
        )

        # 4. 卷积/回踩检测（这里用回踩逻辑示例）
        long_idx, short_idx = [], []
        norm_close = self._normalize(df["close"].to_numpy())

        for i, (row, px, dir_, mid) in enumerate(
            zip(df.iter_rows(named=True), df["close"], df["dir"], df["mid"])
        ):
            if dir_ is None or mid is None:
                continue
            if dir_ == "long" and abs(px - mid) / mid <= self.tol:
                long_idx.append(i)
            if dir_ == "short" and abs(px - mid) / mid <= self.tol:
                short_idx.append(i)

        long_df  = df[long_idx]  if long_idx  else pl.DataFrame()
        short_df = df[short_idx] if short_idx else pl.DataFrame()

        LK.info(f"ConvPatternSignal({freq}): long={len(long_df)} short={len(short_df)}")

        return [
            {
                "name": "Long",
                "start_time": long_df["start_time"].to_list() if len(long_df) else [],
                "datetime": long_df["datetime"].to_list() if len(long_df) else [],
                "marker": dict(color="green", size=12)
            },
            {
                "name": "Short",
                "start_time": short_df["start_time"].to_list() if len(short_df) else [],
                "datetime": short_df["datetime"].to_list() if len(short_df) else [],
                "marker": dict(color="red", size=12)
            }
        ]