import sys
sys.path.append("/home/<USER>/python-workspace/3rdMar/code")
import polars as pl
from datetime import datetime

from quant import signal_backtest, BaseSignal, init_source, init_signal
init_source("/data/bn/spot/1m")

from core.log import LK
# from bollinger import BollingerSignal as MySignal
# from fvg_15 import FvgRtnSignal as MySignal
# from conv import ConvPatternSignal as MySignal
from conv_fvg import ConvFvgSignal as MySignal


# 单例模式封装 Logger 初始化
_logger_initialized = False

def init_logger():
    global _logger_initialized
    if not _logger_initialized:
        LK.init(
            cli_name="wocao",
            loki_url="",
            labels={},
            local_log_level="INFO",
            filedir="/home/<USER>/python-workspace/3rdMar/code",
            filename=f"{MySignal.__name__}",  # 此时 MySignal 已定义
            only_local=True,
        )
        _logger_initialized = True
        LK.info(f"Logger initialized with class: {MySignal.__name__}")

# 在 MySignal 类外部调用初始化
init_logger()

# s = MySignal("15m")
s = MySignal()
init_signal(s)
# res = signal_backtest("BTC-USDT", start_time="20250527", end_time="20250627", returns_interval = [10, 20, 30])
st = "20250527"
et = "20250627"
symbol = "ETH-USDT"
LK.info(f"{symbol} start:{st}, end:{et}")
res = signal_backtest(symbol, start_time=st, end_time=et, returns_interval = [1, 2, 4])

for d, r in res: LK.info(f"{d} return: {r}")
