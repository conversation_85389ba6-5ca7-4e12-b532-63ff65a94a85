import sys
sys.path.append("/home/<USER>/python-workspace/3rdMar/code")

import numpy as np
import polars as pl
from quant import BaseSignal
from core.log import LK

class ConvPatternSignal(BaseSignal):
    """
    用卷积在 1-min K 线里匹配 W / M 型等手工模板
    模板示例：
        W 型 (看涨) : [3,1,3,1,3]   -> 低点-高点-低点-高点-低点
        M 型 (看跌) : [1,3,1,3,1]   -> 高点-低点-高点-低点-高点
    返回的 long / short 信号仍是 1-min 级
    """

    def __init__(self,
                 template_long: list[float] = [3,1,3,0,3],
                 template_short: list[float] = [1,3,1,4,1],
                 threshold: float = 0.85):
        super().__init__()
        self.tpl_long   = np.array(template_long,  dtype=float)
        self.tpl_short  = np.array(template_short, dtype=float)
        self.threshold  = threshold
        LK.info(f"long conv:{self.tpl_long}, short conv:{self.tpl_short}, corr threshold:{self.threshold}")

    # ---------- 工具 ----------
    def _normalize(self, x: np.ndarray) -> np.ndarray:
        """min-max 归一化到 [0,1]"""
        return (x - x.min()) / (x.max() - x.min() + 1e-12)

    def _corr(self, x: np.ndarray, y: np.ndarray) -> float:
        """皮尔逊相关系数"""
        if len(x) != len(y):
            return np.nan
        return np.corrcoef(x, y)[0, 1]

    # ---------- 主逻辑 ----------
    def gen(self, df: pl.DataFrame) -> list[dict]:
        # ---- 转成 numpy ----
        df = df.sort("start_time")
        close_np = df["close"].cast(pl.Float64).to_numpy()
        if len(close_np) < max(len(self.tpl_long), len(self.tpl_short)):
            return [{"name": "Long",  "start_time": [], "datetime": [], "marker": {}},
                    {"name": "Short", "start_time": [], "datetime": [], "marker": {}}]

        # 归一化价格序列
        norm_close = self._normalize(close_np)

        len_long   = len(self.tpl_long)
        len_short  = len(self.tpl_short)

        # 归一化模板
        tpl_long_norm  = self._normalize(self.tpl_long)
        tpl_short_norm = self._normalize(self.tpl_short)

        # ---- 卷积扫描 ----
        long_idx  = []
        short_idx = []

        # 为避免边界越界，从尾部向前滑窗
        for i in range(max(len_long, len_short) - 1, len(norm_close)):
            win_long  = norm_close[i - len_long  + 1 : i + 1]
            win_short = norm_close[i - len_short + 1 : i + 1]

            if self._corr(win_long,  tpl_long_norm)  >= self.threshold:
                long_idx.append(i)
            if self._corr(win_short, tpl_short_norm) >= self.threshold:
                short_idx.append(i)

        # ---- 构造返回 ----
        long_df  = df[long_idx]
        short_df = df[short_idx]

        LK.info(f"ConvPattern: long={len(long_df)}/{len(df)} short={len(short_df)}/{len(df)}")

        return [
            {
                "name": "Long",
                "start_time": long_df["start_time"].to_list(),
                "datetime": long_df["datetime"].to_list(),
                "marker": dict(color="lime", size=10)
            },
            {
                "name": "Short",
                "start_time": short_df["start_time"].to_list(),
                "datetime": short_df["datetime"].to_list(),
                "marker": dict(color="magenta", size=10)
            }
        ]