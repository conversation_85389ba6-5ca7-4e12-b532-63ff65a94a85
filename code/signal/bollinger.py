import sys
sys.path.append("/home/<USER>/python-workspace/3rdMar/code")
import polars as pl
from datetime import datetime

from quant import BaseSignal
from core.log import LK


"""
┌─────────────────────┬─────────────────────┬────────────────┬────────────────┬───┬────────────────┬─────────────┬─────────────────┬────────────┐
│ start_time          ┆ end_time            ┆ open           ┆ high           ┆ … ┆ close          ┆ vol         ┆ money           ┆ trades_cnt │
│ ---                 ┆ ---                 ┆ ---            ┆ ---            ┆   ┆ ---            ┆ ---         ┆ ---             ┆ ---        │
│ i64                 ┆ i64                 ┆ str            ┆ str            ┆   ┆ str            ┆ str         ┆ str             ┆ i64        │
╞═════════════════════╪═════════════════════╪════════════════╪════════════════╪═══╪════════════════╪═════════════╪═════════════════╪════════════╡
"""

class BollingerSignal(BaseSignal):
    # from indicator import init_logger  # 延迟导入
    # init_logger()

    def __init__(self, freq: str = "1m"):
        """
        新增 freq 参数，支持 1m/15m/30m/60m 等任意 polars 支持的窗口字符串
        """
        super().__init__()
        self.freq = freq

    def _resample_kline(self, df: pl.DataFrame) -> pl.DataFrame:
        """
        把 1 分钟 K 线重采样到 self.freq
        聚合规则：open -> first, high -> max, low -> min, close -> last,
                 vol/money/trades_cnt -> sum, start_time/end_time -> first/last
        """
        if self.freq == "1m":
            return df

        # 确保 datetime 是 datetime 类型
        if df["datetime"].dtype != pl.Datetime:
            df = df.with_columns(pl.col("datetime").str.strptime(pl.Datetime, "%Y-%m-%d %H:%M:%S"))

        # 需要聚合的列
        agg_rules = {
            "start_time": pl.col("start_time").first(),
            "end_time": pl.col("end_time").last(),
            "open": pl.col("open").first(),
            "high": pl.col("high").max(),
            "low": pl.col("low").min(),
            "close": pl.col("close").last(),
            "vol": pl.col("vol").sum(),
            "money": pl.col("money").sum(),
            "trades_cnt": pl.col("trades_cnt").sum(),
        }

        # 只保留实际存在的列
        agg_rules = {k: v for k, v in agg_rules.items() if k in df.columns}

        out = (
            df.sort("datetime")
              .group_by_dynamic("datetime", every=self.freq)
              .agg(**agg_rules)
        )

        # 把缺失列（如 end_time 等）用 null 填回，保持与原表列顺序一致
        for col in df.columns:
            if col not in out.columns:
                out = out.with_columns(pl.lit(None).alias(col))

        return out.select(df.columns)
    
    def calculate_bollinger_bands(self, df: pl.DataFrame, window: int = 20, num_std: float = 2) -> pl.DataFrame:
        """
        计算布林带指标（Bollinger Bands）
        
        参数:
            df: 包含股票数据的Polars DataFrame，需包含'datetime'和'close'列
            window: 移动平均窗口大小（默认20）
            num_std: 标准差倍数（默认2）
        
        返回:
            添加了布林带三轨的DataFrame
        """
        if self.freq != "1m":
           df = self._resample_kline(df)
        # 确保数据按时间排序
        df = df.sort("datetime")
        
        # 将价格列转换为浮点数类型（如果原始数据是字符串）
        if df["close"].dtype == pl.Utf8:
            df = df.with_columns(pl.col("close").cast(pl.Float64))
        
        # 计算中轨（20日移动平均）
        middle_band = pl.col("close").rolling_mean(window_size=window)
        
        # 计算标准差
        std_dev = pl.col("close").rolling_std(window_size=window)
        
        # 计算上轨和下轨
        upper_band = middle_band + (std_dev * num_std)
        lower_band = middle_band - (std_dev * num_std)
        bandwidth = upper_band - lower_band
        
        # 添加三轨到原始DataFrame
        result = df.with_columns(
            middle_band.alias("middle_band"),
            upper_band.alias("upper_band"),
            lower_band.alias("lower_band"),
            bandwidth.alias("bandwidth")
        )
        
        return result
    
    def bollinger_crossing_mid(self, df:pl.DataFrame, window:int = 10, num_std:float = 2, lookback = 5):
        df = self.calculate_bollinger_bands(df, window, num_std)
        # print(df)
        tolerance = 30
        prev_conditions_long0 = [
            (pl.col("close").shift(i) > pl.col("lower_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        condition1 = ((pl.col("low") - pl.col("lower_band")).abs() < tolerance) & pl.all_horizontal(prev_conditions_long0) & ((pl.col("low") - pl.col("lower_band")) < 0)
        # condition1 = None

        prev_conditions_long = [
            (pl.col("close").shift(i) < pl.col("middle_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        condition2 = ((pl.col("close") - pl.col("middle_band")).abs() < tolerance) & pl.all_horizontal(prev_conditions_long) & ((pl.col("close") - pl.col("middle_band")) > 0)
        condition2 = None
        df_long = df.filter(condition1|condition2)
        # print(df_long)

        prev_conditions_short = [
            (pl.col("close").shift(i) > pl.col("middle_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        condition3 = ((pl.col("close") - pl.col("middle_band")).abs() < tolerance) & pl.all_horizontal(prev_conditions_short)
        df_short = df.filter(condition3)

        LK.info(f"window:{window}, num_std:{num_std}, lookback:{lookback}")
        LK.info(f"freq:{self.freq}, window:{window}, num_std:{num_std}, lookback:{lookback}, {len(df_long)}/{df.height} long points, {len(df_short)}/{df.height} short points")
        return [
            {
                "name" : "Long",
                "start_time" : df_long["start_time"].to_list(),
                "datetime" : df_long["datetime"].to_list(),
                "marker" : dict(color='yellow', size=10)
            },
            {
                "name" : "Short",
                "start_time" : df_short["start_time"].to_list(),
                "datetime" : df_short["datetime"].to_list(),
                "marker" : dict(color='blue', size=10)
            }
        ]
    
    def bollinger_crossing_band(self, df:pl.DataFrame, window:int = 10, num_std:float = 2, lookback = 3):
        df = self.calculate_bollinger_bands(df, window, num_std)

        df_long = []
        df_short = []
        prev_conditions_long = [
            (pl.col("high").shift(i) > pl.col("upper_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        condition1 = pl.all_horizontal(prev_conditions_long)
        df_long = df.filter(condition1)

        prev_conditions_short = [
            (pl.col("low").shift(i) > pl.col("lower_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        df_short = df.filter(pl.all_horizontal(prev_conditions_short))

        LK.info(f"long conditions: {condition1}")
        LK.info(f"freq:{self.freq}, window:{window}, num_std:{num_std}, lookback:{lookback}, {len(df_long)}/{df.height} long points, {len(df_short)}/{df.height} short points")
        return [
            {
                "name" : "Long",
                "start_time" : df_long["start_time"].to_list(),
                "datetime" : df_long["datetime"].to_list(),
                "marker" : dict(color='yellow', size=10)
            },
            {
                "name" : "Short",
                "start_time" : df_short["start_time"].to_list(),
                "datetime" : df_short["datetime"].to_list(),
                "marker" : dict(color='blue', size=10)
            }
        ]
    
    def bollinger_bandwidth(self, df:pl.DataFrame, window:int = 10, num_std:float = 2, lookback = 3):
        df = self.calculate_bollinger_bands(df, window, num_std)
        df_long = []
        df_short = []

        prev_conditions = [
            (pl.col("bandwidth").shift(i) < pl.col("bandwidth").shift(i-1))
            for i in range(1, lookback + 1)
        ]
        prev_long = [
            (pl.col("high").shift(i) > pl.col("upper_band").shift(i))
            for i in range(1, lookback + 1)
        ]
        # long_condition = (pl.col("high") > pl.col("upper_band")) & pl.all_horizontal(prev_conditions)
        long_condition = pl.all_horizontal(prev_long) & pl.all_horizontal(prev_conditions)
        df_long = df.filter(prev_conditions)

        short_condition = (pl.col("low") < pl.col("lower_band")) & pl.all_horizontal(prev_conditions)
        df_short = df.filter(prev_conditions)
        
        LK.info(f"long conditions: {long_condition}")
        LK.info(f"short conditions: {short_condition}")
        LK.info(f"window:{window}, num_std:{num_std}, lookback:{lookback}, {len(df_long)}/{df.height} long points, {len(df_short)}/{df.height} short points")
        return [
            {
                "name" : "Long",
                "start_time" : df_long["start_time"].to_list(),
                "datetime" : df_long["datetime"].to_list(),
                "marker" : dict(color='yellow', size=10)
            },
            {
                "name" : "Short",
                "start_time" : df_short["start_time"].to_list(),
                "datetime" : df_short["datetime"].to_list(),
                "marker" : dict(color='blue', size=10)
            }
        ]
        
    def gen(self, df:pl.DataFrame):
        # return self.demo(df)
        # return self.momentum(df)
        return self.bollinger_crossing_mid(df)
        # return self.bollinger_crossing_band(df)
        # return self.bollinger_bandwidth(df)