import sys
sys.path.append("/home/<USER>/python-workspace/3rdMar/code")

import polars as pl
from quant import BaseSignal
from core.log import LK

class FvgRtnSignal(BaseSignal):
    """
    回踩最近历史 FVG 中点开仓
    1) 先离线扫描所有历史 FVG -> fvg_table
    2) 再逐分钟判断当前价是否踩到最近同向 FVG 中点
    """

    def __init__(self, min_gap_rate: float = 0.0005, tolerance: float = 0.0015):
        super().__init__()
        self.min_gap_rate = min_gap_rate   # 最小缺口阈值
        self.tolerance    = tolerance      # 允许回踩误差（相对中点）

    # ------------------ 1. 扫描历史 FVG ------------------
    def _scan_all_fvg(self, df: pl.DataFrame) -> pl.DataFrame:
        """返回静态表：每个 FVG 的上下沿、中点、方向、出生时间"""
        df = df.sort("datetime")
        for col in ("open", "high", "low", "close"):
            if df[col].dtype == pl.Utf8:
                df = df.with_columns(pl.col(col).cast(pl.Float64))

        df = df.with_columns(
            # 看涨 FVG
            pl.when(pl.col("low") > pl.col("high").shift(2))
            .then((pl.col("low") + pl.col("high").shift(2)) / 2)  # 中点
            .otherwise(None).alias("mid_long"),

            pl.when(pl.col("low") > pl.col("high").shift(2))
            .then(pl.col("high").shift(2))                         # 上沿
            .otherwise(None).alias("up_long"),

            pl.when(pl.col("low") > pl.col("high").shift(2))
            .then(pl.col("low"))                                   # 下沿
            .otherwise(None).alias("dn_long"),

            # 看跌 FVG
            pl.when(pl.col("high") < pl.col("low").shift(2))
            .then((pl.col("high") + pl.col("low").shift(2)) / 2)
            .otherwise(None).alias("mid_short"),

            pl.when(pl.col("high") < pl.col("low").shift(2))
            .then(pl.col("high"))
            .otherwise(None).alias("up_short"),

            pl.when(pl.col("high") < pl.col("low").shift(2))
            .then(pl.col("low").shift(2))
            .otherwise(None).alias("dn_short"),
        )

        # 过滤微小缺口
        fvg_long = (
            df.filter(pl.col("mid_long").is_not_null())
            .with_columns(
                ((pl.col("dn_long") - pl.col("up_long")) / pl.col("up_long"))
                .alias("gap_rate")
            )
            .filter(pl.col("gap_rate") >= self.min_gap_rate)
            .select(
                pl.col("datetime").alias("born_time"),
                pl.col("mid_long").alias("mid"),
                pl.col("up_long").alias("up"),
                pl.col("dn_long").alias("dn"),
                pl.lit("long").alias("dir"),
            )
        )

        fvg_short = (
            df.filter(pl.col("mid_short").is_not_null())
            .with_columns(
                ((pl.col("up_short") - pl.col("dn_short")) / pl.col("dn_short"))
                .alias("gap_rate")
            )
            .filter(pl.col("gap_rate") >= self.min_gap_rate)
            .select(
                pl.col("datetime").alias("born_time"),
                pl.col("mid_short").alias("mid"),
                pl.col("up_short").alias("up"),
                pl.col("dn_short").alias("dn"),
                pl.lit("short").alias("dir"),
            )
        )

        fvg_table = pl.concat([fvg_long, fvg_short]).sort("born_time")
        return fvg_table

    # ------------------ 2. 逐分钟检测回踩 ------------------
    def gen(self, df: pl.DataFrame) -> list[dict]:
        # 1. 离线拿到所有 FVG
        fvg_table = self._scan_all_fvg(df)

        # 2. 逐分钟检查
        records = []
        df = df.sort("datetime")
        for col in ("open", "high", "low", "close"):
            if df[col].dtype == pl.Utf8:
                df = df.with_columns(pl.col(col).cast(pl.Float64))

        # 用 rolling 找最近 FVG
        long_signals = []
        short_signals = []

        for row in df.iter_rows(named=True):
            ts_now   = row["datetime"]
            px_now   = row["close"]

            # 最近尚未被价格穿破的 LONG FVG
            cand_long = (
                fvg_table
                .filter((pl.col("dir") == "long") & (pl.col("born_time") < ts_now))
                .filter(pl.col("mid") > px_now)          # 价格还没上去
                .tail(1)                                # 最近一个
            )
            if cand_long.height > 0:
                mid = cand_long["mid"][0]
                if abs(px_now - mid) / mid <= self.tolerance:
                    long_signals.append(row)

            # 最近 SHORT FVG
            cand_short = (
                fvg_table
                .filter((pl.col("dir") == "short") & (pl.col("born_time") < ts_now))
                .filter(pl.col("mid") < px_now)
                .tail(1)
            )
            if cand_short.height > 0:
                mid = cand_short["mid"][0]
                if abs(px_now - mid) / mid <= self.tolerance:
                    short_signals.append(row)

        long_df  = pl.DataFrame(long_signals)
        short_df = pl.DataFrame(short_signals)

        LK.info(f"FvgRtnSignal: long={len(long_df)}, short={len(short_df)}, total={len(df)}")
        sdf = 1
        ssie = ""
        return [
            {
                "name": "Long",
                "start_time": long_df["start_time"].to_list() if len(long_df) else [],
                "datetime": long_df["datetime"].to_list() if len(long_df) else [],
                "marker": dict(color="green", size=12)
            },
            {
                "name": "Short",
                "start_time": short_df["start_time"].to_list() if len(short_df) else [],
                "datetime": short_df["datetime"].to_list() if len(short_df) else [],
                "marker": dict(color="red", size=12)
            }
        ]