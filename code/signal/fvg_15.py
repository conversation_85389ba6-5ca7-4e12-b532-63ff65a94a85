import sys
sys.path.append("/home/<USER>/python-workspace/3rdMar/code")

import polars as pl
from quant import BaseSignal
from core.log import LK

class FvgRtnSignal(BaseSignal):
    """
    用 15 分钟 K 线生成 FVG，但开仓点位基于 1 分钟 K 线。
    1) 从 1 分钟数据中提取 15 分钟 FVG
    2) 逐分钟检测价格是否回踩到最近 FVG 中点
    """

    def __init__(self, min_gap_rate: float = 0.0005, tolerance: float = 0.0005):
        super().__init__()
        self.min_gap_rate = min_gap_rate   # 最小缺口阈值
        self.tolerance    = tolerance      # 允许回踩误差（相对中点）

    # ------------------ 1. 从 1 分钟数据中提取 15 分钟 FVG ------------------
    def resample_to_15min_fvg(self, df: pl.DataFrame) -> pl.DataFrame:
        """从 1 分钟数据中提取 15 分钟 FVG"""
        # 按 15 分钟重采样
        df_15min = (
            df
            .with_columns(pl.col("datetime").dt.truncate("15m").alias("15min_bucket"))
            .group_by("15min_bucket")
            .agg(
                pl.col("open").first().alias("open"),
                pl.col("high").max().alias("high"),
                pl.col("low").min().alias("low"),
                pl.col("close").last().alias("close"),
                pl.col("vol").sum().alias("vol"),
                pl.col("money").sum().alias("money"),
                pl.col("trades_cnt").sum().alias("trades_cnt"),
            )
            .sort("15min_bucket")
        )
        print(df_15min)
        # 检测 FVG
        df_15min = df_15min.with_columns(
            # 看涨 FVG
            pl.when(pl.col("low") > pl.col("high").shift(2))
            .then((pl.col("low") + pl.col("high").shift(2)) / 2)  # 中点
            .otherwise(None).alias("mid_long"),

            pl.when(pl.col("low") > pl.col("high").shift(2))
            .then(pl.col("high").shift(2))                         # 上沿
            .otherwise(None).alias("up_long"),

            pl.when(pl.col("low") > pl.col("high").shift(2))
            .then(pl.col("low"))                                   # 下沿
            .otherwise(None).alias("dn_long"),

            # 看跌 FVG
            pl.when(pl.col("high") < pl.col("low").shift(2))
            .then((pl.col("high") + pl.col("low").shift(2)) / 2)
            .otherwise(None).alias("mid_short"),

            pl.when(pl.col("high") < pl.col("low").shift(2))
            .then(pl.col("high"))
            .otherwise(None).alias("up_short"),

            pl.when(pl.col("high") < pl.col("low").shift(2))
            .then(pl.col("low").shift(2))
            .otherwise(None).alias("dn_short"),
        )

        # 过滤微小缺口
        fvg_long = (
            df_15min.filter(pl.col("mid_long").is_not_null())
            .with_columns(
                ((pl.col("dn_long") - pl.col("up_long")) / pl.col("up_long"))
                .alias("gap_rate")
            )
            .filter(pl.col("gap_rate") >= self.min_gap_rate)
            .select(
                pl.col("15min_bucket").alias("born_time"),
                pl.col("mid_long").alias("mid"),
                pl.col("up_long").alias("up"),
                pl.col("dn_long").alias("dn"),
                pl.lit("long").alias("dir"),
            )
        )

        fvg_short = (
            df_15min.filter(pl.col("mid_short").is_not_null())
            .with_columns(
                ((pl.col("up_short") - pl.col("dn_short")) / pl.col("dn_short"))
                .alias("gap_rate")
            )
            .filter(pl.col("gap_rate") >= self.min_gap_rate)
            .select(
                pl.col("15min_bucket").alias("born_time"),
                pl.col("mid_short").alias("mid"),
                pl.col("up_short").alias("up"),
                pl.col("dn_short").alias("dn"),
                pl.lit("short").alias("dir"),
            )
        )

        fvg_table = pl.concat([fvg_long, fvg_short]).sort("born_time")
        return fvg_table

    # ------------------ 2. 逐分钟检测回踩 ------------------
    def gen(self, df: pl.DataFrame) -> list[dict]:
        # 1. 从 1 分钟数据中提取 15 分钟 FVG
        fvg_table = self.resample_to_15min_fvg(df)

        # 2. 逐分钟检查
        records = []
        df = df.sort("datetime")
        for col in ("open", "high", "low", "close"):
            if df[col].dtype == pl.Utf8:
                df = df.with_columns(pl.col(col).cast(pl.Float64))

        # 用 rolling 找最近 FVG
        long_signals = []
        short_signals = []

        for row in df.iter_rows(named=True):
            ts_now   = row["datetime"]
            px_now   = row["close"]

            # 最近尚未被价格穿破的 LONG FVG
            cand_long = (
                fvg_table
                .filter((pl.col("dir") == "long") & (pl.col("born_time") < ts_now))
                .filter(pl.col("mid") > px_now)          # 价格还没上去
                .tail(1)                                # 最近一个
            )
            if cand_long.height > 0:
                mid = cand_long["mid"][0]
                if abs(px_now - mid) / mid <= self.tolerance:
                    long_signals.append(row)

            # 最近 SHORT FVG
            cand_short = (
                fvg_table
                .filter((pl.col("dir") == "short") & (pl.col("born_time") < ts_now))
                .filter(pl.col("mid") < px_now)
                .tail(1)
            )
            if cand_short.height > 0:
                mid = cand_short["mid"][0]
                if abs(px_now - mid) / mid <= self.tolerance:
                    short_signals.append(row)

        long_df  = pl.DataFrame(long_signals)
        short_df = pl.DataFrame(short_signals)

        LK.info(f"FvgRtnSignal: long={len(long_df)}, short={len(short_df)}")

        return [
            {
                "name": "Long",
                "start_time": long_df["start_time"].to_list() if len(long_df) else [],
                "datetime": long_df["datetime"].to_list() if len(long_df) else [],
                "marker": dict(color="green", size=12)
            },
            {
                "name": "Short",
                "start_time": short_df["start_time"].to_list() if len(short_df) else [],
                "datetime": short_df["datetime"].to_list() if len(short_df) else [],
                "marker": dict(color="red", size=12)
            }
        ]