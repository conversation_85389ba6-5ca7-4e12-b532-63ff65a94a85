import os, sys
sys.path.append("/home/<USER>/python-workspace/3rdMar/code")
from core.httptool import gen_base_http_app
from core.enumtype import DataSource, Action
from core.config import CONFIG
from core.log import LK
from core.wstool import WsClientBase as WS
from core.aps import SCHE, IntervalTask, CrontabTask
import core.bot as BOT

import data.QuantumFetch.source as S
import data.QuantumFetch.common as C
from data.QuantumFetch import *

from tornado.ioloop import IOLoop, PeriodicCallback





def help():
    # https://patorjk.com/software/taag/#p=display&f=Slant&t=3rdMarien-QF
    print(f"""
       _____          ____  ___           _                  ____    ______
      |__  /_________/ /  |/  /___ ______(_)__  ____        / __ \  / ____/
       /_ </ ___/ __  / /|_/ / __ `/ ___/ / _ \/ __ \______/ / / / / /_    
     ___/ / /  / /_/ / /  / / /_/ / /  / /  __/ / / /_____/ /_/ / / __/    
    /____/_/   \__,_/_/  /_/\__,_/_/  /_/\___/_/ /_/      \___\_\/_/                     
                                                            VERSION: {version.version}
""")
    cfg_path = sys.argv[1] if len(sys.argv) > 1 else "./conf/default.yaml"
    cfg_path = os.path.abspath(cfg_path)
    if not os.path.exists(cfg_path):
        print(f"Config file [{cfg_path}] does not exist.")
        print(f"""
        Usage: python {os.path.basename(__file__)} [cfg_path]
        """)
        sys.exit(1)
    CONFIG.get_config_from_file(cfg_path)
    print(f"Config file [{cfg_path}] loaded", end="\n\n")


def init_lk():
    LOG_CFG = CONFIG.get("LOG", {})
    LOKI_CFG = LOG_CFG.get("LOKI", {})
    LOCAL_CFG = LOG_CFG.get("LOCAL", {})
    LK.init(
        cli_name = LOG_CFG["NAME"],
        loki_url= LOKI_CFG["LOKI_URL"],
        labels = LOKI_CFG["LABELS"],
        local_log_level = LOCAL_CFG["LOG_LEVEL"],
        filedir = LOCAL_CFG["FILE_DIR"],
        filename = LOCAL_CFG["FILE_NAME"],
        only_local = LOCAL_CFG["ONLY_LOCAL"],
    )
    LK.info(f"Logger({LOG_CFG['NAME']}) initialized.")


def init_scheduler():
    def get_eval(v):
        return eval(v.split(":")[1]) if isinstance(v, str) and v.startswith("eval:") else v

    TASK_CFG = CONFIG.get("TASK", {})
    for _, info in TASK_CFG.items():
        if info.get("ENABLE", False) is False:
            continue
        params = {}
        for k, v in info["PARAMS"].items():
            params[k] = get_eval(v)
        # print(info)
        if info["TASK_TYPE"] == "INTERVAL":
            task = IntervalTask(
                name=info.get("TASK_NAME", None),
                id=info.get("TASK_ID", None),
                seconds=info["TASK_SCHEDULE"],
                func=eval(info["FUNC"]),
                args=params
            )
        elif info["TASK_TYPE"] == "CRONTAB":
            task = CrontabTask(
                name=info.get("TASK_NAME", None),
                id=info.get("TASK_ID", None),
                cron_expression=info["TASK_SCHEDULE"],
                func=eval(info["FUNC"]),
                args=params
            )
        else:
            raise ValueError(f"unknown task type: {info['TASK_TYPE']}")

        SCHE.add_task(task)

    print("Loaded tasks:")
    log_msg = "Loaded tasks:\n"
    for id, t in enumerate(SCHE.task_di.values()):
        print(f"[{id}] {t}")
        log_msg += f"[{id}] {t}\n"
    LK.critical(log_msg)


if __name__ == "__main__":
    help()
    init_lk()
    init_scheduler()

    try:
        app = gen_base_http_app(C.handlers)
        try:
            app.listen(CONFIG["GLOBAL"]["PORT"])
        except:
            app.listen(5111)

        '''timer'''
        PeriodicCallback(lambda: IOLoop.current().spawn_callback(WS.check), 1000 * 60 * 10).start()

        ''' just for test '''
        # IOLoop.current().spawn_callback(S(DataSource.OKX).run, Action.GET_BASIC_INFO)
        # IOLoop.current().spawn_callback(S(DataSource.BINANCE_SPOT).run, Action.GET_BASIC_INFO)
        # IOLoop.current().spawn_callback(S(DataSource.BINANCE_SPOT).run, Action.GET_KLINE, symbol="BNBBTC", interval="30m", start_time=1655969280000, end_time=1655972880000, limit=10)
        # IOLoop.current().spawn_callback(S(DataSource.BINANCE_SPOT).run, Action.GET_KLINE, symbol="BNBBTC", interval="30m", start_time=1655969280000, end_time=1655972880000, limit=10)
        # IOLoop.current().spawn_callback(S(DataSource.BINANCE_SPOT).run, Action.GET_KLINE, symbol="BNBBTC", interval="30m", start_time=1655969280000, end_time=1655972880000, limit=10)
        # IOLoop.current().spawn_callback(S(DataSource.BINANCE_SPOT).run, Action.GET_KLINE, symbol="BNBBTC", interval="30m", start_time=1655969280000, end_time=1655972880000, limit=10)
        # IOLoop.current().spawn_callback(S(DataSource.BINANCE_SPOT).run, Action.GET_KLINE, symbol="BNBBTC", interval="30m", start_time=1655969280000, end_time=1655972880000, limit=10)
        # IOLoop.current().spawn_callback(S(DataSource.BINANCE_SPOT).task, "TASK_BN_HISTORY_KLINE", symbols=["BTC-USDT", "ETH-USDT", "SOL-USDT", "HYPER-USDT"],
        #                                                                                             freq="1m",
        #                                                                                             start_time=0,
        #                                                                                             end_time=1750603023000)
        # IOLoop.current().spawn_callback(S(DataSource.BINANCE_SPOT).task, "TASK_BN_HISTORY_KLINE", symbols=["HYPER-USDT"],
        #                                                                                             freq="1m",
        #                                                                                             start_time=1750503023000,
        #                                                                                             end_time=1750603023000)
        # IOLoop.current().spawn_callback(S(DataSource.BINANCE_SPOT).run, Action.SUB_KLINE, symbols=["BTC-USDT", "ETHUSDT"], freq = "1m")
        # IOLoop.current().spawn_callback(S(DataSource.BINANCE_SPOT).task, "TASK_BN_DOWNLOAD_ALL_KLINE")


        SCHE.start()
        IOLoop.current().start()
    except KeyboardInterrupt:
        SCHE.shutdown()
        IOLoop.current().stop()



'''
TODO:
1. try function -> done
2. config -> done
3. loki -> done
4. bot -> done
5. timer job - from apscheduler.schedulers.tornado import TornadoScheduler -> done
6. retry timer job in background ⚠️ -> handle rate limit
7. daily update okx
8. okx error handler -> error wrapper -> done
9. base class support run & handle -> done
10. failed log -> recover task
11. task priority
'''