from core.base import *

class DataSourceBase(ClassBase):
    def __init__(self):
        self.support_action_li = []
    def _error_wrapper(self, *args, **kwargs):
        pass

    async def run(self, *args, **kwargs):
        return await self.impl_run(*args, **kwargs)
    async def handler(self, *args, **kwargs):
        return await self.impl_handler(*args, **kwargs)
    async def task(self, *args, **kwargs):
        return await self.impl_task(*args, **kwargs)

    @abstractmethod
    async def impl_run(self, *args, **kwargs):
        raise NotImplementedError("impl_run must be implemented in subclass")
    @abstractmethod
    async def impl_handler(self, *args, **kwargs):
        raise NotImplementedError("impl_handler must be implemented in subclass")
    @abstractmethod
    async def impl_task(self, *args, **kwargs):
        raise NotImplementedError("impl_task must be implemented in subclass")
