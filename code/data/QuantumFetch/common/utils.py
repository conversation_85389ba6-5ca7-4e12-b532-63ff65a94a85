from core.config import CONFIG
from core.log import LK

import os, time, asyncio
import functools
from glob import glob
from inspect import iscoroutinefunction
import polars as pl


def pl_write_csv(df:pl.DataFrame, fpath:str):
    if df.is_empty():
        raise ValueError("empty dataframe")
    dir_path = fpath.rsplit("/", 1)[0]
    os.makedirs(dir_path, exist_ok=True)
    df.write_csv(fpath)

def pl_write_parquet(df:pl.DataFrame, fpath:str, time_metadata:dict[str, str] = None):
    if df.is_empty():
        raise ValueError("empty dataframe")
    dir_path = fpath.rsplit("/", 1)[0]
    os.makedirs(dir_path, exist_ok=True)
    df.write_parquet(fpath, metadata=time_metadata)

def default_output(df:pl.DataFrame, fpath:str):
    df.write_parquet(fpath)
def pl_update_parquet(df:pl.DataFrame, fpath:str, size_limit:int = 4294967296, output_df:callable = default_output):
    compression_ratio = 2
    if df.is_empty():
        return
    # LK.info(f"duration_diffs: {set(df.select(pl.col(df.columns[0]).diff()).to_series().to_list())}")
    dir_path = fpath.rsplit("/", 1)[0]
    os.makedirs(dir_path, exist_ok=True)
    idx = 0
    # if os.path.exists(fpath):
    #     df_old = pl.read_parquet(fpath)
    #     df = pl.concat([df_old, df], how="vertical")
    #     df = df.unique(subset=[df.columns[0]]).sort(df.columns[0])
    if fpath_li := glob(fpath + "*"):
        fpath_li = sorted(fpath_li, key=lambda x: int(x.split('.')[-1]))
        idx = int(fpath_li[-1].rsplit(".", 1)[1])
        df_old = pl.read_parquet(fpath_li[-1])
        df = pl.concat([df_old, df], how="vertical")
        df = df.unique(subset=[df.columns[0]]).sort(df.columns[0])

    # last_end = None
    cur_size = df.estimated_size("mb")
    if cur_size > size_limit:
        cur_size_per_row = cur_size / len(df) / compression_ratio
        while cur_size > size_limit:
            n = int(size_limit / cur_size_per_row)
            output_df(df.head(n), f"{fpath}.{idx}")
            # if last_end:
            #     LK.warn(f"dur: {df.head(n).head(1)['start_time'][0] - last_end}")
            # last_end = df.head(n).tail(1)['start_time'][0]
            df = df.slice(n, None)
            cur_size = df.estimated_size("mb")
            # if idx == 0:
            #     if os.path.exists(fpath): os.remove(fpath)
            idx += 1
        if not df.is_empty():
            output_df(df, f"{fpath}.{idx}")
    else:
        # df.write_parquet(fpath)
        output_df(df, f"{fpath}.{idx}")

# params must be ns level timestamps
def create_time_metadata(begin:int, end:int, latest:int):
    if begin < 10**16 or end < 10**16 or latest < 10**16:
        raise ValueError(f"timestamps must be ns level, begin:{begin}, end:{end}, latest:{latest} received")
    return {
        "begin": str(begin),
        "end": str(end),
        "latest": str(latest)
    }

def pl_read_parquet_timemeta(fpath: str) -> dict[str, str]:
    mt = pl.read_parquet_metadata(fpath)
    return {
        "begin": mt["begin"],
        "end": mt["end"],
        "latest": mt["latest"]
    }


def all_keys_in_dict(li, di):
    return all(key in di for key in li)


def try_except(func):
    if iscoroutinefunction(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            if CONFIG.get("GLOBAL", {}).get("DISABLE_TRY_EXCEPT", 0) == 1:
                return await func(*args, **kwargs)
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                LK.error(f"{LK._format_exception(e)}", f_lvl=2)
                return None
        return async_wrapper
    else:
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            if CONFIG.get("GLOBAL", {}).get("DISABLE_TRY_EXCEPT", 0) == 1:
                return func(*args, **kwargs)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                LK.error(f"{LK._format_exception(e)}", f_lvl=2)
                return None
        return sync_wrapper


# max calls in period seconds
def rate_limiter(max_calls: int, period: float):
    def decorator(func):
        timestamps = []

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            nonlocal timestamps
            now = time.time()
            timestamps = [t for t in timestamps if now - t < period] # rm expired timestamps
            if len(timestamps) >= max_calls:
                oldest = timestamps[0]
                wait = period - (now - oldest)
                LK.debug(f"rate limit exceeded. wait {wait:.2f} seconds", f_lvl=2)
                time.sleep(wait)
            timestamps.append(now)
            return func(*args, **kwargs)

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            nonlocal timestamps
            now = time.time()
            timestamps = [t for t in timestamps if now - t < period] # rm expired timestamps
            if len(timestamps) >= max_calls:
                oldest = timestamps[0]
                wait = period - (now - oldest)
                LK.debug(f"rate limit exceeded. wait {wait:.2f} seconds", f_lvl=2)
                await asyncio.sleep(wait)
            timestamps.append(now)
            return await func(*args, **kwargs)

        return async_wrapper if iscoroutinefunction(func) else sync_wrapper
    return decorator

