from core.config import CONFIG
from core.log import LK

import json
import os

# No support of multi-threading
class ApikeyManager():
    """
    Inject ApikeyManager into the datasource class when initializing.
    Use get_key() to get the current used key.
    Use rotate_key() to change key if necessary.
    """
    def __init__(self, datasource:str,  key_list: str, state_file_path: str = 'api_state.json'):
        """
        :param datasource: the name of the datasource this ApikeyManager belongs to
        :param key_list: the apikey(s) read from CONFIG, split with " "
        :param state_file: the file to store the index of current used apikey
        """
        if not key_list or len(key_list) == 0:
            raise ValueError("No API Key received when initializing ApikeyManager")
        
        self.keys = key_list.split(" ")
        self.state_file = state_file_path
        self.num_keys = len(self.keys)
        
        # 加载当前使用的Key的索引
        self.current_index = self._load_state()

        LK.info(f"ApikeyManager initialized for {datasource}, {self.num_keys} keys found", f_lvl=2)
        LK.info(f"current index: {self.current_index} (Key: ...{self.get_key()[-4:]})", f_lvl=2)

    def _save_state(self, index:int):
        dir_path = self.state_file.rsplit("/", 1)[0]
        os.makedirs(dir_path, exist_ok=True)
        try:
            with open(self.state_file, 'w') as f:
                json.dump({"current_key_index": index}, f)
        except IOError as e:
            LK.error(f"failed to save current index to '{self.state_file}': {e}", f_lvl=2)

    def _load_state(self) -> int:
        try:
            with open(self.state_file, 'r') as f:
                state:dict = json.load(f)
                index = state.get("current_key_index", 0)
                # 健壮性检查：如果索引超出范围（比如配置文件中的key变少了），重置为0
                if index >= self.num_keys:
                    LK.warn(f"current_key_index out of range of supported keys, reset to 0", f_lvl=2)
                    return 0
                return index
        except (json.JSONDecodeError, IOError, FileNotFoundError) as e:
            LK.warn(f"failed to load current index from '{self.state_file}': {e}, reset to 0", f_lvl=2)
            self._save_state(0)
        
        return 0



    def get_key(self) -> str:
        return self.keys[self.current_index]

    def rotate_key(self) -> str:
        LK.warn(f"rotating API key, current index: {self.current_index}", f_lvl=2)   
        # 计算下一个索引，使用模运算实现循环
        # (1 % 3 = 1), (2 % 3 = 2), (3 % 3 = 0)
        self.current_index = (self.current_index + 1) % self.num_keys       
        self._save_state(self.current_index)        
        new_key = self.get_key()
        LK.info(f"new index: {self.current_index} (Key: ...{new_key[-4:]})")       
        return new_key