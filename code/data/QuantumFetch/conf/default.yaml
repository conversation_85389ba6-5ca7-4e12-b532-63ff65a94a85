GLOBAL:
  PORT: 5000
  RUN_MODE: 1 # 1: UAT, 2: PROD
  DISABLE_TRY_EXCEPT: 1 # 1: Disable try-except, 0: Enable try-except

LOG:
  NAME: "default-logger"
  LOKI:
    LOKI_URL: "https://loki.example.com"
    LABELS:
      SERVICE: DEFAULT
  LOCAL:
    LOG_LEVEL: INFO
    FILE_DIR: "/data/code/data/QuantumFetch"
    FILE_NAME: "default"
    ONLY_LOCAL: True
  BUFFER_LEN: 40
  BUFFER_SIZE: 1000000

BOT:
  SENTINEL:
    URL: "https://open.feishu.cn/open-apis/bot/v2/hook/0b65bdf5-d869-49da-93ef-a82ada062b00"
    SECRET: "zqBdbLAQibIg6rfzm6MIIb"


MODULE:
  DATASOURCE:
    OKX:
      API_KEY: "your_okx_api_key"
      SECRET: "your_okx_secret_key"
    BN_SPOT:
      WEBSOCKET_COUNT: 5
      PATH_META: "/data/bn/meta"
      PATH_SPOT: "/data/bn/spot"

    
    TOKEN_INSIGHT:
      API_KEY: "35b31ee372f54785a422561b2356d957 db0d95cbae2448be9183c65cc192d536"
      API_KEY_STATE_PATH: "/data/token_insight/api_key_manager/api_state.json"
      COIN_POOL: 15704
      COIN_MKC_PATH: "/data/token_insight/coin_mkc/day"
      COIN_LIST_PATH: "/data/token_insight/coin_list/collected_coins.parquet"
      SYMBOL_LIST_PATH: "/data/token_insight/meta/symbol_list.parquet"
      COIN_MKC_INTERVAL_PATH: "/data/token_insight/coin_mkc/day/collected_coins_interval.parquet" # life cycle of coins marketcap history data {symbol:str, id:str, begin:f64_ms, end:f64_ms, current_market_cap:f64}
    
TASK:
  TEST:
    ENABLE: False
    TASK_NAME: JOJO_TEST
    TASK_ID: test_1
    TASK_TYPE: INTERVAL # CRONTAB / INTERVAL
    TASK_SCHEDULE: 10
    FUNC: S(DataSource.OKX).run
    PARAMS:
      a: eval:Action.GET_KLINE
  BN_SPOT_DAILY_UPDATE_BASIC:
    ENABLE: False
    TASK_NAME: BN_SPOT_DAILY_UPDATE_BASIC
    TASK_ID: BN_SPOT_0
    TASK_TYPE: CRONTAB
    TASK_SCHEDULE: "0 55 2 * * * Asia/Shanghai"
    FUNC: S(DataSource.BINANCE_SPOT).run
    PARAMS:
      a: eval:Action.GET_BASIC_INFO
  BN_SPOT_DAILY_UPDATE_KLINE:
    ENABLE: False
    TASK_NAME: BN_SPOT_DAILY_UPDATE_KLINE
    TASK_ID: BN_SPOT_1
    TASK_TYPE: CRONTAB
    TASK_SCHEDULE: "0 0 3 * * * Asia/Shanghai"
    FUNC: S(DataSource.BINANCE_SPOT).task
    PARAMS:
      task_name: "TASK_BN_DOWNLOAD_ALL_KLINE"
  TOKEN_INSIGHT_SYMBLST_UDT:
    ENABLE: True
    TASK_NAME: TOKEN_INSIGHT_SYMBLST_UPDATE
    TASK_ID: TI_1
    TASK_TYPE: CRONTAB
    TASK_SCHEDULE: "0 00 19 SAT * * * Asia/Shanghai"
    FUNC: S(DataSource.TOKEN_INSIGHT).task
    PARAMS:
      task: update_symbol
  TOKEN_INSIGHT_GET_BASIC_INFO:
    ENABLE: True 
    TASK_NAME: TOKEN_INSIGHT_GET_BASIC_INFO
    TASK_ID: TI_2
    TASK_TYPE: CRONTAB
    TASK_SCHEDULE: "0 10 19 SAT * * * Asia/Shanghai"
    FUNC: S(DataSource.TOKEN_INSIGHT).run
    PARAMS:
      a: eval:Action.GET_BASIC_INFO
      freq: day
  TOKEN_INSIGHT_MKC:
    ENABLE: True 
    TASK_NAME: TOKEN_INSIGHT_MKC
    TASK_ID: TI_3
    TASK_TYPE: CRONTAB
    TASK_SCHEDULE: "0 15 19 SAT * * * Asia/Shanghai"
    FUNC: S(DataSource.TOKEN_INSIGHT).run
    PARAMS:
      a: eval:Action.GET_MARKETCAP
      freq: day

