from common.base import DataSourceBase
from common.utils import *
from common.apikey_manager import Apikey<PERSON>anager

from core.log import LK
from core.enumtype import DataSource, SymbolType, Action, ErrorCode
from core.httptool import async_get_request, async_post_request
from core.config import CONFIG

from tornado.httpclient import HTTPR<PERSON>ponse
from tornado.httputil import url_concat
import polars as pl
import time, json
import glob
import re
import os
import shutil

class MockDataSource(DataSourceBase):
    e:DataSource = DataSource.MOCK
    first_init = True
    def __init__(self):
        if self.first_init:
            global MD_CFG 
            MD_CFG = CONFIG.get("MODULE").get("DATASOURCE").get("MOCK")
            self.first_init = False

        self.keys:str = MD_CFG.get("API_KEY")
        self.expiration = {}
        for i, k in enumerate(self.keys.split(" ")):
            self.expiration[k] = i + 1
            LK.info(f"{k} available for {self.expiration[k]} times")
        
        self.apikey_manager = ApikeyManager(f"{MockDataSource.__name__}", 
                                            MD_CFG.get("API_KEY"), 
                                            "mock_api_state.json")
        self.API_KEY = self.apikey_manager.get_key()
    
    @try_except
    async def impl_run(self, *args, **kwargs):
        LK.info("==================START======================")
        while True:
            if self.expiration[self.API_KEY] > 0:
                self.expiration[self.API_KEY] -= 1
                LK.info(f"{self.API_KEY} called, expires after {self.expiration[self.API_KEY]} calls")
                time.sleep(2)
                continue

            self.API_KEY = self.apikey_manager.rotate_key()
            if self.expiration[self.API_KEY] <= 0:
                LK.warn("all keys are expired")
                break

        return ErrorCode.OK
    @try_except
    async def impl_task(self, *args, **kwargs):
        pass
    @try_except
    async def impl_handler(self, *args, **kwargs):
        pass
