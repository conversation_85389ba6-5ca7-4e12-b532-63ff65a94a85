from common.base import DataSourceBase
from common.utils import *

from core.log import LK
from core.enumtype import DataSource, SymbolType, Action, ErrorCode
from core.httptool import async_get_request, async_post_request


from tornado.httpclient import HTTPResponse
import polars as pl
import time, json


class OKXDataSource(DataSourceBase):
    e:DataSource = DataSource.OKX

    OKX_SUPPORT_ACTION = [
        Action.GET_BASIC_INFO,
        # Action.GET_KLINE
    ]

    def __init__(self):
        self.support_action_li = self.OKX_SUPPORT_ACTION
        self.BASE_URL = "https://www.okx.com"
        self.META_PATH = "/data/okx/meta"
        self.SPOT_PATH = "/data/okx/spot"
        self.PERP_PATH = "/data/okx/perp"

    def _get_ns(self):
        return int(time.time() * 10**9)

    def _error_wrapper(self, response: HTTPResponse):
        if response.code > 300:
            msg = f"HTTP({response.code}): {str(response.reason)}"
            LK.error(msg, f_lvl=2)
            return msg
        try:
            rsp:dict = json.loads(response.body)
            if rsp["code"] != '0':
                msg = rsp.get("msg", "unknow err msg")
                msg = f"{str(ErrorCode.EXTERNAL_ERROR)}: {msg}"
                LK.error(msg, f_lvl=2)
                return msg
            return ""
        except:
            return f"{str(ErrorCode.EXTERNAL_ERROR)}: unknow err msg"


    async def get_basic_info(self): # TODO: compare with the old
        LK.info(f"start", f_lvl=1)
        # API: https://www.okx.com/docs-v5/zh/?python#public-data-rest-api-get-instruments

        url = f"{self.BASE_URL}/api/v5/public/instruments?instType=SPOT"
        response = await async_get_request(url)
        msg = self._error_wrapper(response)
        if len(msg) != 0: return 
        rsp:dict = json.loads(response.body)
        spot_data = rsp["data"]

        url = f"{self.BASE_URL}/api/v5/public/instruments?instType=SWAP"
        response = await async_get_request(url)
        msg = self._error_wrapper(response)
        if len(msg) != 0: return 
        rsp:dict = json.loads(response.body)
        perp_data = rsp["data"]

        def map_symbol_status(status:str) -> SymbolStatus:
            if status == "live": return SymbolStatus.TRADING
            if status == "suspend": return SymbolStatus.BREAK
            return SymbolStatus.UNKNOW

        cur_ns = str(self._get_ns())
        ''' process spot '''
        pair_li = []
        for item in spot_data:
            if item["quoteCcy"] != "USDT" and item["settleCcy"] != "USDT":
                continue
            if item["ctType"] == "inverse":
                continue # 暂时不支持反向合约
            internal_symbol = f"{item['instId']}" if item['instType'] == "SPOT" else f"{str(item['instId']).rsplit('-', 1)[0]}-PERP"
            symbol_type = SymbolType.SPOT if item["instType"] == "SPOT" else SymbolType.PERP_LINEAR
            base_ccy = str(item["baseCcy"]).upper() if item["instType"] == "SPOT" else str(item["ctValCcy"]).upper()
            pair_li.append({
                "exchange": str(self.e),
                "exchangeId": self.e.value,
                "symbol": internal_symbol, # TODO
                "externalSymbol": item["instId"],
                "symbolType": str(symbol_type), # TODO
                "baseCcy": base_ccy,
                "quoteCcy": str(item["quoteCcy"]).upper(),
                "settleCcy": str(item["settleCcy"]).upper(),
                "maxLever": item.get("lever", "0"),
                "tickSz": item.get("tickSz", "0"),
                "lotSz": item.get("lotSz", "0"),
                "minSz": item.get("minSz", "0"),
                "listTime": str(int(item["listTime"]) * 1000_000),
                "expTime": "0",
                'status': map_symbol_status(item["state"]),
                "updateTime": cur_ns
            })
        df = pl.DataFrame(pair_li)
        pl_write_csv(df, f"{self.META_PATH}/spot.csv")

        ''' process perp '''
        pair_li = []
        for item in perp_data:
            if item["quoteCcy"] != "USDT" and item["settleCcy"] != "USDT":
                continue
            if item["ctType"] == "inverse":
                continue # 暂时不支持反向合约
            internal_symbol = f"{item['instId']}" if item['instType'] == "SPOT" else f"{str(item['instId']).rsplit('-', 1)[0]}-PERP"
            symbol_type = SymbolType.SPOT if item["instType"] == "SPOT" else SymbolType.PERP_LINEAR
            base_ccy = str(item["baseCcy"]).upper() if item["instType"] == "SPOT" else str(item["ctValCcy"]).upper()
            pair_li.append({
                "exchange": str(self.e),
                "exchangeId": self.e.value,
                "symbol": internal_symbol, # TODO
                "externalSymbol": item["instId"],
                "symbolType": str(symbol_type), # TODO
                "baseCcy": base_ccy,
                "quoteCcy": str(item["quoteCcy"]).upper(),
                "settleCcy": str(item["settleCcy"]).upper(),
                "maxLever": item.get("lever", "0"),
                "tickSz": item.get("tickSz", "0"),
                "lotSz": item.get("lotSz", "0"),
                "minSz": item.get("minSz", "0"),
                "listTime": str(int(item["listTime"]) * 1000_000),
                "expTime": "0",
                'status': map_symbol_status(item["state"]),
                "updateTime": cur_ns
            })
        df = pl.DataFrame(pair_li)
        pl_write_csv(df, f"{self.META_PATH}/perp.csv")
        return ErrorCode.OK


    @try_except
    async def impl_handler(self, symbol:str):
        pass

    @try_except
    async def impl_run(self, a:Action):
        if a == Action.GET_BASIC_INFO:
            return await self.get_basic_info()
        else:
            LK.error(f"{str(ErrorCode.ACTION_NOT_SUPPORT)}: {str(a)}")
            return ErrorCode.ACTION_NOT_SUPPORT

    @try_except
    async def impl_task(self, a:Action):
        pass

