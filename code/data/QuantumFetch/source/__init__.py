from core.enumtype import DataSource

from .okx import OKXDataSource
from .tokeninsight import TIDataSource
from .binance_spot import BN_SPOT_DataSource


DataSourceMap = {
    DataSource.OKX: OKXDataSource,
    DataSource.BINANCE_SPOT: BN_SPOT_DataSource,
    DataSource.TOKEN_INSIGHT: TIDataSource,
}



class _SourceModule:
    def __call__(self, e: DataSource):
        return DataSourceMap[e]()
import sys
sys.modules[__name__] = _SourceModule()
