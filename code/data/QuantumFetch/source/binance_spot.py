from common.base import DataSourceBase
from common.utils import *

from core.wstool import WsClientBase as WS
from core.log import LK
from core.config import CONFIG
import core.bot as BOT
from core.enumtype import DataSource, SymbolType, Action, ErrorCode, SymbolStatus
from core.httptool import async_get_request, async_post_request

from typing import Optional, Dict, List
from tornado.httpclient import HTTPResponse
from tornado.ioloop import IOLoop
import polars as pl
import time, json, asyncio, os
from enum import StrEnum
from datetime import datetime
import pytz

CFG = None

def bn_output_parquet(df:pl.DataFrame, fpath:str):
    meta = {
        "start_time": str(df.item(0, 0)),       # 第一行第一列
        "end_time": str(df.item(-1, 1)),        # 最后一行第二列
        "update_time_utc+8": datetime.now(pytz.timezone('Asia/Shanghai')).strftime("%Y-%m-%d %H:%M:%S")
    }
    df.write_parquet(fpath, metadata=meta)


class BN_RateLimitType(StrEnum):
    UNKNOW = "UNKNOW"
    REQUEST_WEIGHT = "REQUEST_WEIGHT"
    ORDERS = "ORDERS"
    RAW_REQUESTS = "RAW_REQUESTS"



class BN_SPOT_WS(WS):
    def __init__(self, name: str, url: str, ssl:Optional[Dict] = None, pool_name:str = str(DataSource.BINANCE_SPOT)):
        super().__init__(name, url, ssl, pool_name)
        self.disconnect_on_idle = True
        LK.info(str(self))

    async def test_connectivity(self):
        request_id = f"BN_SPOT_{int(time.time() * 1000_000_000)}"
        request_message = {
            "method": "ping",
            "id": request_id
        }
        await self.snd_message(request_message, request_id)

    async def handle_ws_message(self, msg):
        # error handling -> maybe do it in DataSource
        try:
            data = json.loads(msg)
            msg_id = data.get("id") # BN use this as request id
            if msg_id is not None and msg_id in self.pending_futures:
                future = self.pending_futures.pop(msg_id)
                future.set_result(data)
            else:
                # 處理非請求回應類型的消息
                pass
        except Exception as e:
            LK.error(f"Error handling ws message: {e}")



class BN_SPOT_SUB_WS(WS):
    def __init__(self, name: str, url: str, ssl:Optional[Dict] = None, pool_name:str = f"{str(DataSource.BINANCE_SPOT)}:SUB"):
        super().__init__(name, url, ssl, pool_name)
        self.disconnect_on_idle = False
        LK.info(str(self))

    async def test_connectivity(self):
        request_id = f"BN_SPOT_SUB_{int(time.time() * 1000_000_000)}"
        request_message = {
            "method": "ping",
            "id": request_id
        }
        await self.snd_message(request_message, request_id)

    async def handle_ws_message(self, msg):
        try:
            data = json.loads(msg)
            msg_id = data.get("id") # BN use this as request id
            if msg_id is not None and msg_id in self.pending_futures:
                future = self.pending_futures.pop(msg_id)
                future.set_result(data)
            else:
                print(data)
                pass
        except Exception as e:
            LK.error(f"Error handling ws message: {e}")



class BN_SPOT_DataSource(DataSourceBase):   
    e:DataSource = DataSource.BINANCE_SPOT

    BN_SPOT_SUPPORT_ACTION = [
        Action.GET_BASIC_INFO,
        Action.GET_KLINE
    ]

    first_init = True
    RATELIMIT_INFO = {} # assume that we only run one thread in one process
    SYMBOL_MAP = {}

    def __new__(cls):
        if cls.first_init:
            global CFG
            CFG = CONFIG["MODULE"]["DATASOURCE"]["BN_SPOT"]
            # print(CFG)
            for i in range(CFG["WEBSOCKET_COUNT"]):
                BN_SPOT_WS(f"bn_spot_ws_{int(time.time() * 10**9)}", "wss://ws-api.binance.com:443/ws-api/v3") # must be here, otherwise will gen too may sockets
            cls.first_init = False
        instance = super().__new__(cls)
        return instance

    def __init__(self):
        self.support_action_li = self.BN_SPOT_SUPPORT_ACTION
        self.BASE_WS_SUB_URL = "wss://stream.binance.com:9443/ws"
        self.BASE_REST_URL = "https://api.binance.com"
        self.BASE_REST_DATAVISION_URL = "https://data-api.binance.vision"
        self.META_PATH = CFG["PATH_META"]
        self.SPOT_PATH = CFG["PATH_SPOT"]

        # update symbol map when init
        self._update_symbol_map()


    def _update_symbol_map(self, update_local:bool = False):
        fpath = f"{self.META_PATH}/spot.csv"
        if not os.path.exists(fpath) or update_local:
            df = asyncio.run(self.get_basic_info())
        df = pl.read_csv(fpath)
        self.SYMBOL_MAP = {k: v["externalSymbol"] for k, v in df.rows_by_key(key=["symbol"], named=True, unique=True).items()}

    def _get_ws_msg_id(self):
        return f"BN_SPOT_{int(time.time() * 1000_000_000)}"

    def _get_ns(self):
        return int(time.time() * 10**9)

    def _get_time(self):
        return time.time()

    def _error_wrapper(self, response: HTTPResponse):
        if response.code > 300:
            msg = f"HTTP({response.code}): {str(response.reason)}"
            LK.error(msg, f_lvl=2)
            return msg
        try:
            rsp:dict = json.loads(response.body)
            if rsp.get("status", 200) != 200:
                msg = rsp.get("msg", "unknow err msg")
                LK.error(msg, f_lvl=2)
                LK.error(f"{response.body}", f_lvl=2)
                return ErrorCode.EXTERNAL_ERROR, msg
            return ErrorCode.OK, ""
        except:
            return ErrorCode.EXTERNAL_ERROR, f"unknow err msg"

    def _error_wrapper_ws_response(self, rsp:dict):
        if rsp.get("status", 0) != 200:
            msg = rsp.get("msg", "unknow err msg")
            LK.error(msg, f_lvl=2)
            return ErrorCode.EXTERNAL_ERROR, msg
        return ErrorCode.OK, ""


    async def _check_bn_ratelimit(self,
                                cur_time: float,
                                bn_ratelimit_type: str = None,
                                weight: int = 0,
                                ratelimit_info: dict = None,
                                ):
        # https://developers.binance.com/docs/zh-CN/binance-spot-api-docs/enums#速率限制种类ratelimittype
        '''before snd requet'''
        if ratelimit_info == None:
            if self.RATELIMIT_INFO == None: return
            if bn_ratelimit_type not in self.RATELIMIT_INFO: return

            limit = self.RATELIMIT_INFO[bn_ratelimit_type]["limit"]
            count = self.RATELIMIT_INFO[bn_ratelimit_type]["count"]
            # keep 20 in case, depends on the amount of connection we create
            if count + weight <= limit - 30:
                return
            # might reach the ratelimit here, start to control
            if self.RATELIMIT_INFO[bn_ratelimit_type]["interval"] == "SECOND": divisor = 1
            elif self.RATELIMIT_INFO[bn_ratelimit_type]["interval"] == "MINUTE": divisor = 60
            else: return
            interval = self.RATELIMIT_INFO[bn_ratelimit_type]["intervalNum"]
            last_time = self.RATELIMIT_INFO[bn_ratelimit_type]["last_time"]
            time_gap = cur_time / divisor - last_time / divisor
            if time_gap >= interval:
                return
            time_to_wait = (interval - time_gap) * divisor
            LK.warn(f"bn ratelimit wait for {time_to_wait}s", f_lvl = 2)
            await asyncio.sleep(time_to_wait)
            return

        '''after recv response'''
        bn_ratelimit_type = ratelimit_info["rateLimitType"]
        if bn_ratelimit_type not in self.RATELIMIT_INFO:
            self.RATELIMIT_INFO[bn_ratelimit_type] = {}
            self.RATELIMIT_INFO[bn_ratelimit_type]["interval"] = ratelimit_info["interval"]
            self.RATELIMIT_INFO[bn_ratelimit_type]["intervalNum"] = ratelimit_info["intervalNum"]
        self.RATELIMIT_INFO[bn_ratelimit_type]["limit"] = ratelimit_info["limit"]
        self.RATELIMIT_INFO[bn_ratelimit_type]["count"] = ratelimit_info["count"]
        self.RATELIMIT_INFO[bn_ratelimit_type]["last_time"] = cur_time

    def _gen_bn_kline_dataframe(self, data: list):
        if len(data) == 0:
            return None
        columns = [
            "start_time", "end_time", "open", "high", "low", "close", "vol", "money", "trades_cnt"
        ]
        return pl.DataFrame(data, schema=columns, orient="row")


    async def get_basic_info(self):
        # API: https://developers.binance.com/docs/zh-CN/binance-spot-api-docs/rest-api/general-endpoints#交易规范信息
        LK.info(f"start", f_lvl=1)
        cur_ns = str(self._get_ns())
        # url = f"{self.BASE_REST_URL}/api/v3/exchangeInfo"
        url = f"{self.BASE_REST_DATAVISION_URL}/api/v3/exchangeInfo"
        response = await async_get_request(url)
        code, msg = self._error_wrapper(response)
        if code != ErrorCode.OK:
            return code, msg

        def map_symbol_status(status:str) -> SymbolStatus:
            if status == "TRADING": return SymbolStatus.TRADING
            if status == "END_OF_DAY": return SymbolStatus.CLOSE
            if status == "HALT": return SymbolStatus.HALT
            if status == "BREAK": return SymbolStatus.BREAK
            return SymbolStatus.UNKNOW

        rsp = json.loads(response.body)
        symbols = rsp["symbols"]
        pair_li = []
        cur_ns = self._get_ns()
        for item in symbols:
            if item["quoteAsset"] != "USDT":
                continue
            internal_symbol = f"{item['baseAsset']}-{item['quoteAsset']}" 
            pair_li.append({
                "exchange": str(self.e),
                "exchangeId": self.e.value,
                "symbol": internal_symbol,
                "externalSymbol": item["symbol"],
                "symbolType": str(SymbolType.SPOT),
                "baseCcy": item["baseAsset"],
                "quoteCcy": str(item["quoteAsset"]).upper(),
                "settleCcy": str(item["quoteAsset"]).upper(),
                "maxLever": None,
                "tickSz": item["filters"][0].get("tickSize", "0"),
                "lotSz": item["filters"][1].get("stepSize", "0"),
                "minSz": item["filters"][1].get("minQty", "0"),
                "listTime": "0",
                "expTime": "0",
                'status': map_symbol_status(item["status"]),
                "updateTime": cur_ns
            })
        df = pl.DataFrame(pair_li)
        pl_write_csv(df, f"{self.META_PATH}/spot.csv")
        self._update_symbol_map()
        return ErrorCode.OK, df

    async def sub_kline(self, symbols:List[str], freq:str):
        # API: https://developers.binance.com/docs/zh-CN/binance-spot-api-docs/web-socket-streams#utc-k线
        # TODO ratelimit check
        ws = BN_SPOT_SUB_WS(f"bn_spot_ws_sub_{self._get_ns()}", self.BASE_WS_SUB_URL)
        conn:BN_SPOT_SUB_WS = await WS.get_conn(f"{str(self.e)}:SUB")
        if conn is None:
            LK.error(f"{str(ErrorCode.CONNECTIONS_NOT_AVAILABLE)}: WebSocket connection not found")
            return ErrorCode.CONNECTIONS_NOT_AVAILABLE
        request_id = self._get_ws_msg_id()
        bn_symbols = [i if '-' not in i else self.SYMBOL_MAP[i] for i in symbols]
        params = [f"{i.lower()}@kline_{freq}" for i in bn_symbols]
        request_message = {
            "method": "SUBSCRIBE",
            "params": params,
            "id": request_id
        }
        rsp = await conn.snd_message(request_message, request_id)
        if rsp["result"] != None:
            LK.error(f"sub kline failed for {symbols}")
            LK.error(f"{rsp}")
            return ErrorCode.EXTERNAL_ERROR
        LK.info(f"sub kline success for {symbols}")
        return ErrorCode.OK

    async def get_kline(self, symbol:str, freq:str, start_time:int, end_time:int, limit:int):
        # API: https://developers.binance.com/docs/zh-CN/binance-spot-api-docs/websocket-api/market-data-requests#k线数据
        # LK.info(f"start", f_lvl=1) # Too Many Log
        cur_time = self._get_time()
        await self._check_bn_ratelimit(cur_time, bn_ratelimit_type=BN_RateLimitType.REQUEST_WEIGHT.value, weight=2)
        conn:BN_SPOT_WS = await WS.get_conn(str(self.e))
        if conn is None:
            LK.error(f"{str(ErrorCode.CONNECTIONS_NOT_AVAILABLE)}: WebSocket connection not found")
            return ErrorCode.CONNECTIONS_NOT_AVAILABLE, ""
        request_id = self._get_ws_msg_id()
        request_message = {
            "method": "klines",
            "params": {
                "symbol": symbol, # should be in BN symbol format
                "interval": freq,
                "startTime": start_time,
                "limit": limit
            },
            "id": request_id
        }
        rsp = await conn.snd_message(request_message, request_id)
        await conn.release()
        await self._check_bn_ratelimit(cur_time, ratelimit_info=rsp["rateLimits"][0]) #[{'rateLimitType': 'REQUEST_WEIGHT', 'interval': 'MINUTE', 'intervalNum': 1, 'limit': 6000, 'count': 4}]
        code, msg = self._error_wrapper_ws_response(rsp)
        if code != ErrorCode.OK:
            return code, msg

        data = rsp["result"]
        res = {
            "start_time": start_time,
            "end_time": start_time,
            "data": []
        }
        if len(data) == 0:
            LK.warn(f"{symbol} from ts({start_time}) recv empty klines info")
            return ErrorCode.OK, res

        res["start_time"] = data[0][0]
        res["end_time"] = data[-1][6]
        for item in data:
            tmp = [
                item[0] * 1000_000,         # kline start time (int)
                item[6] * 1000_000,         # kline end time (int)
                float(item[1]),             # kline open (float)
                float(item[2]),             # kline high (float)
                float(item[3]),             # kline low (float)
                float(item[4]),             # kline close (float)
                float(item[5]),             # kline vol (float)
                float(item[7]),             # kline money (float)
                int(item[8]),               # kline trades count (int)
            ]
            res["data"].append(tmp)
        return ErrorCode.OK, res


    async def task_history_kline(self, symbols:list, freq:str, start_time:int, end_time:int):
        res = ErrorCode.ERROR
        finish_time_di = {i: start_time for i in symbols}
        cache_di = {i: {
            "start_time": 0,
            "end_time": 0,
            "data": []
        } for i in symbols}
        min_finish_time = end_time
        while True:
            for symbol in symbols:
                code, rsp = await self.get_kline(self.SYMBOL_MAP[symbol],
                                    freq,
                                    finish_time_di[symbol],
                                    end_time,
                                    1000)
                if code != ErrorCode.OK:
                    if code == ErrorCode.CONNECTIONS_NOT_AVAILABLE:
                        await asyncio.sleep(1)
                        continue
                    else:
                        LK.error(f"sth wrong in task_history_kline({str(code)})")
                        break
                # update finish_time_di & min_finish_time
                finish_time_di[symbol] = rsp["end_time"] + 1
                min_finish_time = min(min_finish_time, finish_time_di[symbol])
                # save data to cache
                cache_di[symbol]["data"].extend(rsp["data"])
                # if cache is too large -> save to disk / share-memory / in-memory DB
                if (len(cache_di[symbol]["data"]) >= 60_000                         # reach cache limit
                        or finish_time_di[symbol] >= end_time                       # reach user's end_time limit
                        or rsp["end_time"] - cache_di[symbol]["end_time"] < 10      # crazy small endtime diff
                    ):
                    if len(cache_di[symbol]["data"]) > 0: # maybe nothing to update
                        df = self._gen_bn_kline_dataframe(cache_di[symbol]["data"])
                        fpath = f"{self.SPOT_PATH}/{freq}/{symbol}.parquet"
                        pl_update_parquet(df, fpath, 50, bn_output_parquet)
                    cache_di[symbol]["data"] = []
                    if (finish_time_di[symbol] >= end_time                          # reach user's end_time limit
                        or rsp["end_time"] - cache_di[symbol]["end_time"] < 10      # crazy small endtime diff
                    ):
                        symbols.remove(symbol)
                        LK.info(f"{symbol}({freq}) processed: 100%, remain ({len(symbols)})")
                    else:
                        LK.info(f"{symbol}({freq}) processed: {int(100*(1 - (end_time - finish_time_di[symbol]) / (end_time - cache_di[symbol]['start_time'])))}%")
                # update time
                if cache_di[symbol]["start_time"] == 0:
                    cache_di[symbol]["start_time"] = rsp["start_time"]
                cache_di[symbol]["end_time"] = rsp["end_time"]
            # check if we should continue
            if len(symbols):
                continue
            res = ErrorCode.OK
            break
        return res




    @try_except
    async def impl_handler(self):
        pass

    @try_except
    async def impl_run(self, a:Action, **k):
        if a == Action.GET_BASIC_INFO:
            return await self.get_basic_info()
        elif a == Action.GET_KLINE:
            # if not all_keys_in_dict(["symbol", "freq", "start_time", "end_time", "limit"], k):
            #     self.lk.error(f"missing param in {str(a)}")
            #     return ErrorCode.PARAM_NOT_SUPPORT
            return await self.get_kline(k["symbol"], k["freq"], k["start_time"], k["end_time"], k["limit"])
        elif a == Action.SUB_KLINE:
            return await self.sub_kline(k["symbols"], k["freq"])
        else:
            LK.error(f"{str(ErrorCode.ACTION_NOT_SUPPORT)}: {str(a)}")
            return ErrorCode.ACTION_NOT_SUPPORT

    @try_except
    async def impl_task(self, task_name:str, **k):
        if task_name == "TASK_BN_HISTORY_KLINE":
            res = await self.task_history_kline(k["symbols"], k["freq"], k["start_time"], k["end_time"])
            LK.warn(f"task {task_name} code({str(res)})")
        if task_name == "TASK_BN_DOWNLOAD_ALL_KLINE":
            freq = '1m'
            start_time = 0
            end_time = int(time.time() * 1000)
            df = pl.read_csv(f"{self.META_PATH}/spot.csv")
            symbols = df["symbol"].to_list()
            batch_size = 1
            symbol_batches = [symbols[i:i + batch_size] for i in range(0, len(symbols), batch_size)]
            LK.info(f"sep into {len(symbol_batches)} symbol batches")
            tasks = []
            for idx, batch in enumerate(symbol_batches):
                LK.info(f"symbol {batch[0]} ({idx}/{len(symbol_batches)}) start")
                '''test'''
                # await self.task_history_kline(batch, freq, start_time, end_time)
                '''resume'''
                # batch_size must be 1, if you want to correctly resume
                if fpath_li := glob(f"{self.SPOT_PATH}/{freq}/{batch[0]}" + "*"):
                    fpath_li = sorted(fpath_li, key=lambda x: int(x.split('.')[-1]))
                    meta = pl.read_parquet_metadata(fpath_li[-1])
                    start_time = int(meta["end_time"])
                    start_time = int(start_time / 1000_000 + 1)
                    data_time_diff = end_time - start_time
                    if data_time_diff > 3 * 24 * 60 * 60 * 1000:
                        LK.info(f"no need to update {batch[0]}, time diff: {data_time_diff}")
                        continue

                    last_update_time = datetime.strptime(meta["update_time_utc+8"], "%Y-%m-%d %H:%M:%S")
                    last_update_time = pytz.timezone('Asia/Shanghai').localize(last_update_time)
                    if (datetime.now(pytz.timezone('Asia/Shanghai')) - last_update_time).total_seconds() <= 60 * 60:
                        LK.info(f"no need to update {batch[0]}, from last udpate no more tham 1h")
                        continue
                    LK.info(f"resume {batch[0]} from {start_time}")

                tasks.append(self.task_history_kline(batch, freq, start_time, end_time))
                if (idx + 1) % CFG["WEBSOCKET_COUNT"] == 0:
                    await asyncio.gather(*tasks)
                    tasks.clear()
            if tasks:
                await asyncio.gather(*tasks)

            BOT("SENTINEL").send("BN_SPOT_DAILY_UPDATE_KLINE done")
            LK.info(f"TASK_BN_DOWNLOAD_ALL_KLINE done")
