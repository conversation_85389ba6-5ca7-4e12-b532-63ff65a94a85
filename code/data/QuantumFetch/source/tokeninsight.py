# # 获取当前脚本的父目录的父目录（即 QuantumFetch 目录）
import os
import sys
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.append("/home/<USER>/python-workspace/3rdMar/code")

from common.base import DataSourceBase
from common.utils import *
from common.apikey_manager import ApikeyManager

from core.log import LK
from core.enumtype import DataSource, SymbolType, Action, ErrorCode
from core.httptool import async_get_request, async_post_request
from core.config import CONFIG
import core.bot as BOT

from tornado.httpclient import HTTPResponse
from tornado.httputil import url_concat
import polars as pl
import time, json
import glob
import gc
import os
import shutil


class TIDataSource(DataSourceBase):
    e:DataSource = DataSource.TOKEN_INSIGHT

    TI_SUPPORT_ACTION = [
        Action.GET_BASIC_INFO,
        Action.GET_MARKETCAP
    ]
    first_init = True

    def __init__(self):
        if self.first_init:
            global TI_CFG
            TI_CFG = CONFIG.get("MODULE").get("DATASOURCE").get("TOKEN_INSIGHT")
            self.first_init = False

        self.support_action_li = self.TI_SUPPORT_ACTION
        self.BASE_URL = "https://api.tokeninsight.com/api/v1/"    
        self.COIN_LIST_PATH = TI_CFG.get("COIN_LIST_PATH", "/data/token_insight/coin_list/collected_coins.parquet") 
        self.COIN_MKC_PATH = TI_CFG.get("COIN_MKC_PATH", "/data/token_insight/coin_mkc/day")
        self.SYMBOL_LIST_PATH = TI_CFG.get("SYMBOL_LIST_PATH", "/data/token_insight/meta/symbol_list.parquet")
        self.COIN_MKC_INTERVAL_PATH = TI_CFG.get("COIN_MKC_INTERVAL_PATH", "/data/token_insight/coin_mkc/day/collected_coins_interval.parquet")

        self.apikey_manager = ApikeyManager(self.__class__.__name__, TI_CFG.get("API_KEY"), TI_CFG.get("API_KEY_STATE_PATH"))
        self.TI_API_KEY = self.apikey_manager.get_key()
        self.COIN_POOL = TI_CFG.get("COIN_POOL") 

        self.MAX_RETRY = 3


    def _get_ns(self):
        return int(time.time() * 10**9)

    def _convert_ms_to_ns(self, timestamp:int):
        return timestamp * 10**6

    # 不用处理数据源可信问题
    def _error_wrapper(self, response: HTTPResponse):
        if response.code > 300:
            msg = f"HTTP({response.code}): {str(response.reason)}"
            if response.code == 401 or response.code == 403:
                self.TI_API_KEY = self.apikey_manager.rotate_key()
            LK.error(msg, f_lvl=2)
            return msg
        try:
            rsp:dict = json.loads(response.body)
            if rsp["status"]["code"] != 0: 
                msg = rsp.get("message", "unknow err msg")
                status_code = rsp["status"]["code"] 
                msg = f"{str(ErrorCode.EXTERNAL_ERROR)}: {msg}, TI status code: {status_code}"
                LK.error(msg, f_lvl=2)
                return msg
            return ""
        except:
            return f"{str(ErrorCode.EXTERNAL_ERROR)}: unknow err msg"


    def _calc_history_mkc_length(self, start_ns: int, end_ns: int, freq: str) -> int:
        interval_ns_dict = {
            "minute": 60 * 10**9,        # 60秒 → 60 × 10^9 ns
            "hour": 3600 * 10**9,        # 3600秒 → 3600 × 10^9 ns
            "day": 86400 * 10**9         # 86400秒 → 86400 × 10^9 ns
        }

        max_length_dict = {
            "minute": 10080,  # 最大分钟条数[1](@ref)
            "hour": 8784,     # 最大小时条数[1](@ref)
            "day": 365        # 最大天条数[1](@ref)
        }

        interval_ns = interval_ns_dict[freq]
        max_possible = (end_ns - start_ns) // interval_ns - 1 # the latest data is yesterday
        max_length = max_length_dict.get(freq, 10080)
        return min(max_possible, max_length)

    def _get_ti_headers(self):

        return {
            'TI_API_KEY': self.TI_API_KEY,
            'accept': 'application/json',
            'User-Agent': 'python-requests/2.28.1',
            'Accept-Encoding': 'gzip, deflate',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        }

    def _filter_ti_coin_list_by_symbol_list(self, df:pl.DataFrame, symbol_list_path:str = "code/data/QuantumFetch/source/symbol_list.parquet") -> pl.DataFrame:
        """
        filter coin list by symbol_list.parquet

        params:
        df (polars.DataFrame): raw coin list
        symbol_list_path (str): path of symbol_list.parquet

        returns:
        polars.DataFrame: filtered coin list
        """
        try:
            if not os.path.exists(symbol_list_path):
                LK.error(f"symbol list not found: {symbol_list_path}")
                return df

            symbol_df = pl.read_parquet(symbol_list_path)
            # filter coin list by symbol_list, keep the one with largest spot_volume_24h if there are multiple coins with the same symbol
            filtered_df = df.filter(pl.col("symbol").is_in(symbol_df["symbol"])).sort("spot_volume_24h", descending=True).unique(subset=["symbol"], keep="first")
            LK.info(f"{len(filtered_df)} coins filtered", f_lvl=2)
            return filtered_df

        except Exception as e:
            LK.error(f"filter_ti_coin_list_by_symbol_list Error: {LK._format_exception(e)}")
            return df
    
    
    def _get_symbol_list(self, extract_path: str = "/data/bn/meta/spot.csv", output_path:str = "symbol_list.parquet" ):
        """
        scheduled task to generate symbol list
        """
        bninfo_df = pl.read_csv(extract_path)
        df = bninfo_df.select(pl.col("baseCcy").alias("symbol"))
        output_path = self.SYMBOL_LIST_PATH
        pl_write_parquet(df, output_path)


        
    @rate_limiter(50, 60)
    async def _fetch_coin_list(self, limit:int = 1500, offset:int = 0, vs_currency:str = "usd"):
        """
        fetch coin list from tokeninsight
        API: https://tokeninsight-api.readme.io/reference/get_coins-list

        params:
        limit (int): number of coins to return, max 1500
        offset (int): offset of the list
        vs_currency (str): vs currency, default usd

        returns:
        polars.DataFrame: coin list
        """
        
        url = f"{self.BASE_URL}coins/list"
        params = {
            "limit": limit,
            "offset": offset,
            "vs_currency": vs_currency
        }
        url = url_concat(url, params)
        headers = self._get_ti_headers()

        response = await async_get_request(url, headers=headers)
        msg = self._error_wrapper(response)
        if len(msg) != 0: return

        rsp:dict = json.loads(response.body)
        coins_data = rsp["data"]["items"]

        if (len(coins_data) == 0) or coins_data is None:
            return 

        df = pl.DataFrame([{
            "price": coin.get("price"),
            "symbol": coin.get("symbol"),
            "id": coin.get("id"),
            "spot_volume_24h": coin.get("spot_volume_24h"),
            "price_change_percentage_24h": coin.get("price_change_percentage_24h")
        } for coin in coins_data])

        return df
    
    @rate_limiter(50,60)
    async def _fetch_coin_mkc_history(self, coin_id:str, interval:str = "day", length:str = -1, vs_currency:str = "usd"):
        """
        fetch coin mkc history from tokeninsight
        API: https://tokeninsight-api.readme.io/reference/get_history-coins-id

        params:
        coin_id (str): coin id in token insight
        interval (str): time interval, default day
        length (int): length of the history, max -1, default -1
        vs_currency (str): vs currency, default usd

        returns: df: raw data of one coin
            #    symbol: symbol of the coin, used for path name and interval file
            #    first_timestamp: first timestamp of the history, used for interval file
            #    last_timestamp: last timestamp of the history, used for interval file
            #    current_market_cap: current market cap of the coin, used for interval file

        """
        url = f"{self.BASE_URL}history/coins/{coin_id}"
        params = {
            "interval": interval,
            "length": length,
            "vs_currency": vs_currency
        }
        url = url_concat(url, params)
        headers = self._get_ti_headers()
               
        response = await async_get_request(url, headers=headers)
        msg = self._error_wrapper(response)
        if len(msg) != 0:   
            return None, coin_id, None, None, None

        rsp:dict = json.loads(response.body)  
        history_data = rsp["data"]["market_chart"]
        symbol = rsp["data"]["symbol"]

        if not history_data: 
            LK.debug(f"No history data for {coin_id}")
            return None, symbol, None, None, None

        dtype = {
                "price":        pl.Float64,
                "vol_spot_24h": pl.Float64,
                "market_cap":   pl.Float64,
                "timestamp":    pl.Int64,
                "symbol":       pl.String,
                "id":           pl.String,
            }

        df = pl.DataFrame(
            [{
                "price":        item.get("price"),
                "vol_spot_24h": item.get("vol_spot_24h"),
                "market_cap":   item.get("market_cap"),
                "timestamp":    item.get("timestamp"),
                "symbol":       symbol,
                "id":           coin_id,
            } for item in history_data],
            schema=dtype,
        )


        first_timestamp = history_data[0].get("timestamp")
        is_invalid = df.tail(1).select(pl.col("timestamp") == 0).item()
        if is_invalid:
            df = df.slice(0, len(df) - 1)       
        last_timestamp = df.tail(1).select(pl.col("timestamp")).item()
        current_market_cap = history_data[0].get("market_cap")

        return df, symbol, first_timestamp, last_timestamp, current_market_cap

    # get base info,保留更新接口（日频？周频？）,业务代码尽量无状态，至少状态保证在module内；recovery 
    async def get_coin_list(self):
        """
        fetch full coin list from token insight. 
        no interval in params, update frequency determined by scheduler.  
        """
        FETCH_LIMIT = 1500

        LK.info("start")
        full_coins_df = None
        offset = 0
        total_batches = (self.COIN_POOL + FETCH_LIMIT - 1) // FETCH_LIMIT
        batches_count = 0
        fail_offset = []
        
        begin_timestamp = self._get_ns()
        while offset < self.COIN_POOL:
            batches_count += 1
            current_limit = min(FETCH_LIMIT, self.COIN_POOL - offset)  
            batch_df = await self._fetch_coin_list(limit=current_limit, offset=offset)           
            
            if batch_df is None:
                LK.error(f"batch {batches_count}/{total_batches} returns with None, offset {offset}")
                fail_offset.append(offset)
                offset += current_limit
                continue

            if full_coins_df is None:
                full_coins_df = batch_df
            else:
                full_coins_df = pl.concat([full_coins_df, batch_df])
            
            offset += current_limit

            if len(batch_df) < current_limit:       
                break
        
        full_coins_path = self.COIN_LIST_PATH
        time_metadata = create_time_metadata(
            begin_timestamp,
            begin_timestamp,
            begin_timestamp,
            )
        pl_write_parquet(full_coins_df, full_coins_path, time_metadata=time_metadata)

        if len(fail_offset) > 0:
            fail_offset_df = pl.DataFrame(fail_offset)
            fail_offset_path = os.path.join(self.COIN_LIST_PATH[:-23], "fail_offest.parquet")
            pl_write_parquet(fail_offset_df, fail_offset_path, time_metadata)

        LK.info(f"task completed, coins collected: {len(full_coins_df)}, {len(fail_offset)}/{total_batches} batchs return with None")

        symbol_list_path = self.SYMBOL_LIST_PATH
        filtered_df = self._filter_ti_coin_list_by_symbol_list(full_coins_df, symbol_list_path)

        return filtered_df


    async def _get_filtered_coin_list(self):
        """
        return filtered coin list as Dataframe if local coin list exist, 
        otherwhise get coin list from token insight
        """
        coin_list_df = None
        coin_list_path = self.COIN_LIST_PATH
        if os.path.exists(coin_list_path):
            df = pl.read_parquet(coin_list_path)
            symbol_list_path = self.SYMBOL_LIST_PATH
            coin_list_df = self._filter_ti_coin_list_by_symbol_list(df, symbol_list_path)
            LK.info(f"{coin_list_df.height} coins filtered from local coin list", f_lvl=2)
        else: 
            coin_list_df = await self.get_coin_list()
        
        return coin_list_df
        
    async def collect_coin_mkc_history(self, freq:str="day"):
        """
        fetch coin marketcap history for the supported coins (i.e /data/bn) from token insight. 
        if marketcap history data file exists, update to latest (incremental);
        if not, fetch full marketcap history data and save.

        params:
        (Deprecated) freq(str): by default "day". 

        saved data format: 
        {symbol}_{id}_history_mkc.parquet: {price, vol_spot_24h, market_cap, timestamp, symbol, id}
        collected_coins_interval.parquet: {symbol, id, begin, end, current_market_cap}
        """
        LK.info("start")
        full = False
        inc = False
        partly_full = False

        coin_list_df = await self._get_filtered_coin_list()
        if coin_list_df is None or coin_list_df.is_empty():
            LK.error("coin_list_df is None")
            return ErrorCode.INTERNAL_ERROR
        coin_id_data = coin_list_df.select(["id", "symbol"]).to_dicts()

        symbols_to_add = None
        symbols_to_drop = None
        valid_files = glob.glob(os.path.join(self.COIN_MKC_PATH, "*_history_mkc.parquet"))
        if not valid_files: #full collection
            LK.warn(f"no data file exists in {self.COIN_MKC_PATH}, start FULL collection")
            full = True
            def recreate_directory(directory: str) -> None:
                if os.path.exists(directory):
                    shutil.rmtree(directory)      # 彻底删除目录及内容
                os.makedirs(directory, exist_ok=True)  # 重建空目录
            recreate_directory(self.COIN_MKC_PATH)
        else: # inc collection
            dtype = {
                "price":        pl.Float64,
                "vol_spot_24h": pl.Float64,
                "market_cap":   pl.Float64,
                "timestamp":    pl.Int64,
                "symbol":       pl.String,
                "id":           pl.String,
            }
            existed_df = pl.read_parquet(valid_files, schema=dtype).sort("timestamp", descending=True)
            LK.info("start incremental collection")
            symbols_to_add = coin_list_df["symbol"].filter(
                ~coin_list_df["symbol"].is_in(existed_df["symbol"])
            )
            symbols_to_drop = set(
                existed_df.filter(
                    ~existed_df["symbol"].is_in(coin_list_df["symbol"])
                )["symbol"].unique()
            )
            if symbols_to_add.len() > 0:
                partly_full = True
                LK.info(f"{symbols_to_add.len()} symbols to add")
            inc = True
      
        tasks:list[tuple[str, str, int, pl.DataFrame]] = [] # (symbol, id, length, old_df(optional))
        ns_now = self._get_ns()
        if inc: # load inc tasks
            for symbol, group_df in existed_df.group_by("symbol", maintain_order=True):
                start_timestamp = group_df["timestamp"].max()
                id = group_df["id"][0]
                length = self._calc_history_mkc_length(start_timestamp*10**6, ns_now, freq)
                if symbol[0] in symbols_to_drop:
                    LK.warn(f"{symbol[0]}({id}) is deprecated in the latest symbol_list")
                    continue
                tasks.append((symbol[0], id, length, group_df))
        LK.info(f"{len(tasks)} update tasks loaded")

        if full or partly_full: # load full tasks
            full_count = 0
            if partly_full:
                coin_id_data = coin_list_df.filter(~coin_list_df['symbol'].is_in(existed_df['symbol'])).to_dicts()
            for data in coin_id_data:
                id = data["id"]
                symbol = data["symbol"]
                tasks.append((symbol, id, -1, None))
                full_count += 1
            LK.info(f"{full_count} full collection tasks loaded")

        del existed_df
        gc.collect()
        interval_info = []
        fail_count = 0
        update_count = 0
        collection_count = 0
        null_col_count = 0
        latest_count = 0
        for symbol, id, length, old_df in tasks:
            if length == 0: 
                interval_info.append({
                    "symbol": symbol,
                    "id": id,
                    "begin": old_df["timestamp"].min(),
                    "end": old_df["timestamp"].max(),
                    "current_market_cap": old_df["market_cap"][0]
                })
                latest_count += 1
                continue

            new_df, symbol, latest_timestamp, initial_timestamp, current_market_cap = await self._fetch_coin_mkc_history(id, interval=freq, length=length)
            
            if new_df is None or new_df.is_empty(): # retry immediately if unexpected response caught
                mode = "full" if length == -1 else "inc"
                success = False
                LK.error(f"{mode} mkc collection of {symbol}({id}) got empty response, retrying {self.MAX_RETRY} times")
                for i in range(self.MAX_RETRY):
                    await asyncio.sleep(3)
                    LK.debug(f"retrying {mode} {symbol}({id}) for {i+1} time")
                    new_df, symbol, latest_timestamp, initial_timestamp, current_market_cap = await self._fetch_coin_mkc_history(id, interval=freq, length=length)
                    if new_df is not None and not new_df.is_empty():
                        success = True
                        LK.info(f"retry successed for {symbol}({id})")
                        break

                if not success:
                    LK.error(f"{mode} mkc history ({freq}) collection failed for {symbol}({id}) after {self.MAX_RETRY} times, skipped")
                    fail_count += 1
                    if old_df is not None: # to cover the possible collected but not recorded coins
                        interval_info.append({
                            "symbol": symbol,
                            "id": id,
                            "begin": old_df["timestamp"].min(),
                            "end": old_df["timestamp"].max(),
                            "current_market_cap": old_df["market_cap"][0]
                        })
                    continue
            
            if new_df["market_cap"].null_count() == new_df.height:
                LK.debug(f"{symbol} has a null column of market_cap")
                null_col_count += 1
                new_df = new_df.with_columns(pl.lit(0.0).cast(pl.Float64).alias("market_cap"))

            file_name = f"{symbol}_{id}_history_mkc.parquet" 
            time_metadata = None
                       
            if length > 0:  # incremental colletion
                initial_timestamp = old_df["timestamp"].min()
                update_count += 1
                new_df = pl.concat([new_df, old_df])
            
            # 检查时间戳是否为有效整数（非零且为整数）
            if isinstance(initial_timestamp, int) and isinstance(latest_timestamp, int) \
            and initial_timestamp > 0 and latest_timestamp > 0:          
                time_metadata = create_time_metadata(
                    self._convert_ms_to_ns(initial_timestamp), 
                    self._convert_ms_to_ns(latest_timestamp),
                    self._convert_ms_to_ns(latest_timestamp)
                )
            else:
                # 处理无效时间戳：使用备用值并记录错误
                time_metadata = create_time_metadata(ns_now, ns_now, ns_now)
                LK.error(f"{symbol}({id}) got invalid timestamps: initial={initial_timestamp}, latest={latest_timestamp}")
            
            file_path = os.path.join(self.COIN_MKC_PATH, file_name)                     
            pl_write_parquet(new_df, file_path, time_metadata)
            interval_info.append({
                "symbol": symbol,
                "id": id,
                "begin": initial_timestamp,
                "end": latest_timestamp,
                "current_market_cap": current_market_cap
            })
            collection_count += 1
            
        full_collection_count = collection_count - update_count
        LK.info(f"{collection_count}/{len(tasks)} coins successfully processed! {update_count} updates, {full_collection_count} full collections, {null_col_count} coins has null marketcap, {latest_count} coins has already updated")

        # save interval
        if len(interval_info) > 0: # if any update done, merge the interval info
            interval_df = pl.DataFrame(interval_info)
            time_metadata = create_time_metadata(ns_now, ns_now, ns_now)
            interval_path = self.COIN_MKC_INTERVAL_PATH
            if full or (not os.path.exists(interval_path)):
                pl_write_parquet(interval_df, interval_path, time_metadata)
                mode = "full" if full else "incremental"
                LK.info(f"{mode} mkc collection task completed, new interval data saved") 
                return ErrorCode.OK
            
            old_interval = pl.read_parquet(interval_path)
            update_columns = ["begin", "end", "current_market_cap"]
            merged_df = (
                pl.concat([old_interval, interval_df])  # 临时合并新旧数据
                .group_by(["symbol", "id"])  # 按主键分组
                .agg(  # 每组取最新数据（新数据优先）
                    pl.col(update_columns).last(),  # 更新列取新值
                    pl.exclude(update_columns).first()  # 其他列保留旧值（若有）
                )
            )
            pl_write_parquet(merged_df, interval_path, time_metadata)
            LK.info(f"{merged_df.height} intervals saved, {len(interval_info)} intervals updated")
        LK.info(f"incremental mkc collection task completed")
        return ErrorCode.OK
    
    @try_except
    async def impl_run(self, a:Action, freq:str="day"): 
        if a == Action.GET_BASIC_INFO:
            await self.get_coin_list()
            BOT("SENTINEL").send("TOKEN_INSIGHT_GET_BASIC_INFO done")
            return ErrorCode.OK
        elif a == Action.GET_MARKETCAP:
            await self.collect_coin_mkc_history(freq)
            BOT("SENTINEL").send("TOKEN_INSIGHT_MKC done")
            return ErrorCode.OK
        return ErrorCode.INTERNAL_ERROR

    @try_except
    async def impl_task(self, *args, **kwargs):
        task = kwargs.get("task")
        if task == None:
            return ErrorCode.PARAM_NOT_SUPPORT
        if task == "update_symbol":
            self._get_symbol_list()
            LK.info(f"task {task} done")
            BOT("SENTINEL").send(f"TOKEN_INSIGHT_SYMBLST_UPDATE done")
            return ErrorCode.OK
        
        return ErrorCode.OK


    @try_except
    async def impl_handler(self, a:Action):
        pass

