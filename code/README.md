# Basic Code for 3rdMar

## Crontab Format -> CronTrigger
If `TASK_SCHEDULE` has 6 params (exlcuding timezone): 
```shell
* * * * * *
| | | | | |
| | | | | +----- 年份
| | | | +------- 月份 (1 - 12)
| | | +--------- 日期 (1 - 31)
| | +----------- 小时 (0 - 23)
| +------------- 分钟 (0 - 59)
+--------------- 秒 (0 - 59)
```
If `TASK_SCHEDULE` has 7 params (exlcuding timezone): 
```shell
* * * * * * *
| | | | | | |
| | | | | | +--- 占位符 (用于区分第四位为日期的TASK_SCHEDULE格式)
| | | | | +----- 年份
| | | | +------- 月份 (1 - 12)
| | | +--------- 星期几 (MON - SUN)
| | +----------- 小时 (0 - 23)
| +------------- 分钟 (0 - 59)
+--------------- 秒 (0 - 59)

```