import polars as pl
from datetime import datetime
import pytz
from glob import glob
from time import time

from .common import BaseSignal
from .visu import BaseCanvas

my_signal : BaseSignal = None
my_cv : BaseCanvas = BaseCanvas(name="Signal Analysis")
my_source = "/data/bn/spot/1m"

'''0 - base function'''
def init_signal(signal: BaseSignal):
    global my_signal
    my_signal = signal

def init_source(src:str):
    global my_source
    my_source = src

def gen_signal(df: pl.DataFrame) -> pl.DataFrame:
    if my_signal is None:
        raise ValueError(f"unknown signal")
    return my_signal.gen(df)

# ns timestamp
def load_df(symbol, start_time:int, end_time:int, strict:bool=True):
    fpath_li = glob(f"{my_source}/{symbol}*")
    if len(fpath_li) == 0:
        return None
    fpath_li = sorted(fpath_li, key=lambda x: int(x.split('.')[-1]))
    df_li = []
    for fpath in fpath_li:
        mt = pl.read_parquet_metadata(fpath)
        if int(mt["end_time"]) < start_time:
            continue
        if int(mt["start_time"]) > end_time:
            break
        df_li.append(pl.read_parquet(fpath))
    res = pl.concat(df_li, how="vertical")
    if strict:
        res = res.filter(
            pl.col("start_time").is_between(start_time, end_time, closed="left")
        )
    return res


def get_nanosecond_timestamp(date_str):
    formats = ["%Y%m%d", "%Y-%m-%d"]
    for fmt in formats:
        try:
            dt = datetime.strptime(date_str, fmt)
            # Make it timezone-aware (UTC)
            dt = dt.replace(tzinfo=pytz.utc)
            # Convert to nanoseconds since epoch
            nanoseconds = int(dt.timestamp() * 1_000_000_000)
            return nanoseconds
        except ValueError:
            continue
    
    raise ValueError("Invalid date format. Please use YYYYMMDD or YYYY-MM-DD.")



def signal_backtest(symbol:str,
                    **kwargs):
    start_time = get_nanosecond_timestamp(kwargs["start_time"]) if "start_time" in kwargs else 0
    end_time = get_nanosecond_timestamp(kwargs["end_time"]) if "end_time" in kwargs else int(time.time() * 10**9) # TODO core.time
    returns_interval = kwargs["returns_interval"] if "returns_interval" in kwargs else []

    df = load_df(symbol, start_time, end_time)
    df = df.with_columns(
        pl.from_epoch(pl.col("start_time"), time_unit="ns").alias("datetime")
    )
    df = df.cast({"open": pl.Float64, "high": pl.Float64, "low": pl.Float64, "close": pl.Float64})
    signal_set = gen_signal(df)

    # returns
    if len(returns_interval) > 0: # TODO:适配降采样
        df = df.with_columns(
            # ((pl.col("open").shift(-n) - pl.col("open")) / pl.col("open")).alias(f"long_return_{n}") for n in returns_interval
        [
            ((pl.col("open").shift(-n) - pl.col("open")) / pl.col("open")).alias(f"long_return_{n}")
            for n in returns_interval
        ]+[
            ((pl.col("open") - pl.col("open").shift(-n)) / pl.col("open").shift(-n)).alias(f"short_return_{n}")
            for n in returns_interval
        ]
        )
        res = []
        for signals in signal_set:
            if signals["name"] == "Long":
                return_df = df.filter(pl.col("start_time").is_in(signals["start_time"]))
                cols = [f"long_return_{i}" for i in returns_interval]
                result = return_df.select(
                    [pl.mean(col).alias(f"{col}_mean") for col in cols] +
                    [pl.var(col).alias(f"{col}_var") for col in cols]
                )
                print(f"long return\n", result)
                res.append(("Long",result))
            if signals["name"] == "Short":
                return_df = df.filter(pl.col("start_time").is_in(signals["start_time"]))
                cols = [f"short_return_{i}" for i in returns_interval]
                result = return_df.select(
                    [pl.mean(col).alias(f"{col}_mean") for col in cols] +
                    [pl.var(col).alias(f"{col}_var") for col in cols]
                )
                print(f"short return\n", result)
                res.append(("Short",result))

    # kline
    my_cv.kline(df, signal_set).show()
    return res


