from abc import ABC, abstractmethod
from typing import Dict, Callable, Any, List, Union
from joblib import Parallel, delayed
from .utils import FunctionExecutor, Cache, get_param_dict

class ParallelBase(ABC):
    C: Cache = Cache()
    def __init__(self, 
                n_proc:int = 2):
        self.F: FunctionExecutor = FunctionExecutor()
        self.n_proc = n_proc
    

class MapReduce(ParallelBase):
    FUNC_INIT = "FUNC_INIT"
    FUNC_MAP = "FUNC_MAP"
    FUNC_REDUCE = "FUNC_REDUCE"
    FUNC_PROCESS = "FUNC_PROCESS"

    def __init__(self,
                n_proc:int = 2):
        super().__init__(n_proc)

    def set_init_function(self, func: Callable[..., None], params: Dict[str, Any]):
        self.F.register_function(self.FUNC_INIT, func, params)
    def set_map_function(self, func: Callable[..., None], params: Dict[str, Any]):
        self.F.register_function(self.FUNC_MAP, func, params)
    def set_reduce_function(self, func: Callable[..., None], params: Dict[str, Any]):
        # TODO: checkout params has "results"
        self.F.register_function(self.FUNC_REDUCE, func, params)
    def set_process_function(self, func: Callable[..., None], params: Dict[str, Any]):
        self.F.register_function(self.FUNC_PROCESS, func, params)
    
    def run(self, kwargs:dict):
        self.F.update_param(self.FUNC_INIT, kwargs)
        self.C.data["mapable"] = self.F.functions[self.FUNC_INIT](**self.F.params[self.FUNC_INIT])

        self.F.update_param(self.FUNC_MAP, self.C.data["mapable"])
        self.C.data["processable"] = self.F.functions[self.FUNC_MAP](**self.F.params[self.FUNC_MAP])

        self.C.data["reducable"] = Parallel(n_jobs=self.n_proc)(
            delayed(self.F.functions[self.FUNC_PROCESS])(**p) for p in self.C.data["processable"]
        )

        self.F.update_param(self.FUNC_REDUCE, {"results": self.C.data["reducable"]})
        return self.F.functions[self.FUNC_REDUCE](**self.F.params[self.FUNC_REDUCE])
    

