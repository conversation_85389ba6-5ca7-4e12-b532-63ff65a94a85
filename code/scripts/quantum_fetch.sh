#!/bin/bash

# 激活conda环境
source /home/<USER>/anaconda3/etc/profile.d/conda.sh
conda activate jo311

# 进入项目目录
pushd ../data/QuantumFetch > /dev/null

# 配置文件路径
CONFIG_FILE="./conf/qf-5111.yaml"
LOG_FILE="nohup.log"

# 1. 查找并杀死正在运行的相同配置的进程
OLD_PID=$(ps -aux | grep "$CONFIG_FILE" | grep -v "grep" | awk '{print $2}')
if [ ! -z "$OLD_PID" ]; then
    echo "发现正在运行的进程(PID:$OLD_PID)，正在停止..."
    kill $OLD_PID
    sleep 2  # 等待进程完全停止
    # 再次检查是否停止成功
    if ps -p $OLD_PID > /dev/null; then
        echo "警告：进程(PID:$OLD_PID)仍在运行，强制终止..."
        kill -9 $OLD_PID
    fi
    echo "已停止旧进程(PID:$OLD_PID)"
else
    echo "没有找到正在运行的相同配置的进程"
fi

# 2. 启动新进程
echo "正在启动新进程..."
nohup python main.py $CONFIG_FILE > $LOG_FILE 2>&1 &
NEW_PID=$!

# 3. 显示新进程信息
echo "新进程已启动:"
ps -p $NEW_PID -o pid,cmd

# 返回原目录
popd > /dev/null